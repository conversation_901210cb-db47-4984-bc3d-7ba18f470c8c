<?php
/**
 * Dropdown Shortcode
 *
 * Creates a dropdown/accordion element.
 *
 * Usage:
 * [dropdown title="Title Here" outlined="true" fullwidth="true"]Description content here[/dropdown]
 *
 * Parameters:
 * - title: The clickable title/question text (required)
 * - outlined: Whether to show outlined style (true/false, optional, defaults to false)
 * - fullwidth: Whether to use full width (true/false, optional, defaults to false)
 * - class: Additional CSS classes (optional)
 */

function add_dropdown_shortcode($atts, $content = null)
{
    // Parse shortcode attributes
    $atts = shortcode_atts([
        'title' => '',
        'outlined' => 'false',
        'fullwidth' => 'false',
        'class' => '',
    ], $atts, 'dropdown');

    // Use content between tags as description
    $description = !empty($content) ? trim($content) : '';

    // Validate required attributes
    if (empty($atts['title']) || empty($description)) {
        return '';
    }

    // Sanitize and prepare content
    $title = esc_html(trim($atts['title']));
    $description = wpautop(wp_kses_post($description));

    // Build CSS classes
    $classes = ['dropdown', 'js-dropdown'];

    // Add outlined class if enabled
    if (filter_var($atts['outlined'], FILTER_VALIDATE_BOOLEAN)) {
        $classes[] = 'sp-block-faq__item--outlined';
    }

    // Add any additional custom classes
    if (!empty($atts['class'])) {
        $classes[] = esc_attr($atts['class']);
    }

    $dropdownClass = implode(' ', $classes);

    // Build inline styles for width control
    $styles = [];
    if (filter_var($atts['fullwidth'], FILTER_VALIDATE_BOOLEAN)) {
        $styles[] = 'width: 100% !important';
    } else {
        $styles[] = 'width: auto !important';
        // For outlined elements, also override display to prevent full width
        if (filter_var($atts['outlined'], FILTER_VALIDATE_BOOLEAN)) {
            $styles[] = 'display: inline-block !important';
        }
    }

    // Apply width style to the correct element based on outlined setting
    $isOutlined = filter_var($atts['outlined'], FILTER_VALIDATE_BOOLEAN);
    $isFullWidth = filter_var($atts['fullwidth'], FILTER_VALIDATE_BOOLEAN);
    $outerStyleAttr = '';
    $innerStyleAttr = '';
    $titleStyleAttr = '';

    if ($isOutlined) {
        // For outlined style, apply width to inner div
        $innerStyleAttr = !empty($styles) ? ' style="' . implode('; ', $styles) . '"' : '';
    } else {
        // For non-outlined style, apply width to outer div
        $outerStyleAttr = !empty($styles) ? ' style="' . implode('; ', $styles) . '"' : '';

        // For non-outlined full-width, make the inner elements behave like outlined version
        if ($isFullWidth) {
            $innerStyleAttr = ' style="display: block !important; width: 100% !important;"';
            $titleStyleAttr = ' style="display: flex !important; justify-content: space-between !important;"';
        }
    }

    // Generate and return the dropdown HTML
    return sprintf(
        '<div class="sp-block-faq__item"%s><div class="%s"%s><a class="dropdown__title js-dropdown-trigger" href="#"%s>%s <i class="dropdown__arrow">%s</i></a><div class="dropdown__text">%s</div></div></div>',
        $outerStyleAttr,
        $dropdownClass,
        $innerStyleAttr,
        $titleStyleAttr,
        $title,
        vector('bottom'),
        $description
    );
}

add_shortcode('dropdown', 'add_dropdown_shortcode');
