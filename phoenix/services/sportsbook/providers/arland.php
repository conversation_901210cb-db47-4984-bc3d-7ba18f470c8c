<?php

// CRON to fetch <PERSON>rland every 15 minutes

$arlandCronPrefix = "px_matches_fetch_arland_";

/* Seperate Sportsbook cron for each authority,
since it takes time to fetch sportsbook data,
this cant be done in a foreach/while loop with less code,
because it will throw timeout before it finishes fetching for all authorities */
add_action($arlandCronPrefix . 'SGA', function () {
    px_arland_cron_handler('SGA');
});

add_action($arlandCronPrefix . 'MGA', function () {
    px_arland_cron_handler('MGA');
});

add_action($arlandCronPrefix . 'GGA', function () {
    px_arland_cron_handler('GGA');
});

add_action($arlandCronPrefix . 'DGA', function () {
    px_arland_cron_handler('DGA');
});

add_action($arlandCronPrefix . 'PGA', function () {
    px_arland_cron_handler('PGA');
});

/**
 * Safe cron handler that properly initializes WordPress environment for ArlandAPI
 */
function px_arland_cron_handler($authority) {
    try {
        // Ensure we have access to WordPress functions
        if (!function_exists('get_transient')) {
            return false;
        }

        // Load required files
        require_once(__DIR__ . '/api/ArlandAPI.php');

        // Create API instance and set authority
        $ArlandAPI = ArlandAPI::getInstance();
        $ArlandAPI->authority = $authority;

        // Call the static init method properly
        ArlandAPI::init();

        // Fetch matches
        ArlandAPI::getMatches();

        // if (defined('WP_DEBUG') && WP_DEBUG) {
        //     error_log("ArlandAPI cron executed successfully for authority: $authority");
        // }

    } catch (Exception $e) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("ArlandAPI cron failed for authority $authority: " . $e->getMessage());
        }
    }
}

// Schedule cron events for authorities that have actions defined
$availableAuthorities = ['SGA', 'MGA', 'GGA', 'DGA', 'PGA'];

foreach ($availableAuthorities as $authority) {
    $cronHook = $arlandCronPrefix . $authority;

    // Only schedule if the action exists and it's not already scheduled
    if (has_action($cronHook)) {
        if (!wp_next_scheduled($cronHook)) {
            // Try to use every_fifteen_minutes, fallback to hourly if not available
            $schedules = wp_get_schedules();
            $recurrence = isset($schedules['every_fifteen_minutes']) ? 'every_fifteen_minutes' : 'hourly';

            $scheduled = wp_schedule_event(time(), $recurrence, $cronHook);
            if ($scheduled === false) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("Failed to schedule cron event: $cronHook with recurrence: $recurrence");
                }
            } else {
                // if (defined('WP_DEBUG') && WP_DEBUG) {
                //     error_log("Successfully scheduled cron event: $cronHook with recurrence: $recurrence");
                // }
            }
        }
    }
}

// Populate Select a Sport field with the right sport IDs from Arland API
function acf_load_arland_sport_ids($field)
{

    // Populate sport ids from matches transient
    $populatedOptions  = [];

    $sports = get_transient('px_api_matches_arland_' . CURRENT_AUTHORITY);

    if (is_string($sports)) {
        $sports = unserialize($sports);
    }

    if (is_array($sports)) {
        foreach ($sports as $sport) {
            if (is_string($sport)) $sport = unserialize($sport);
            if (!empty($sport['SportID']) && !empty($sport['SportName'])) {
                $populatedOptions[$sport['SportID']] = $sport['SportName'];
            }
            continue;
        }
    }

    // reset field options
    $field['choices'] = [];

    // default sport as Football
    $field['default_value'] = "1";

    // add options to the select field
    foreach ($populatedOptions as $sportID => $sportName) {
        $field['choices'][$sportID] = $sportName;
    }
    return $field;
}

add_filter('acf/load_field/name=select_a_sport', 'acf_load_arland_sport_ids');

/**
 * Pulls matches from the API based on parameters
 *
 * @param int $sportID is the sport we want to get the matches from
 * @param int $leagueID is optional but helps fetching data quicker from cache
 *
 * If @params are present then it will return the data from cache
 *
 * @return Array of matches from cache
 *
 */
function getMatches(int $sportID = 0, int $leagueID = 0)
{
    $filtered = get_transient('px_api_matches_arland_' . CURRENT_AUTHORITY);

    // Check if league can be found in the existing cache and skip the request
    if (isset($filtered[$sportID][$leagueID])) {
        return $filtered[$sportID][$leagueID];
    } elseif ($leagueID != 0) {
        do_action('qm/warning', 'League ' . $leagueID . ' for Sport ' . $sportID . ' not found in cache');
    }
    return $filtered[$sportID] ?? $filtered;
}

/**
 * Function that gets details about a given match based on parameters
 *
 * @param int $sportID takes the ID of the sport [1, 2, 3, 8, 10] we are getting the match from
 * @param int $matchID takes the ID of the match we are searching for
 * @param int $leagueID is optional but treat as required because helps fetching data from cache quicker
 *
 * @return Array that contains all details for the match
 */
function getMatch(int $sportID, int $matchID, int $leagueID = 0)
{
    // See if we have the result in our 10 minutes cache
    $matches = getMatches($sportID, $leagueID);

    if (empty($matches)) {
        return '"Empty array"';
    }

    // If league ID is provided then we can try fetching the match from cache
    if ($leagueID) {
        if (isset($matches[$matchID])) {
            return $matches[$matchID];
        }
    }

    // Search for the match in all leagues
    foreach ($matches as $leagues) {
        // Not a league array item = Name of the sport
        if (!is_array($leagues)) {
            continue;
        }

        foreach ($leagues as $match) {
            // Not a match item = Name of the league
            if (!isset($match['GameID'])) {
                continue;
            }

            // Actual match item
            if ($match['GameID'] == $matchID) {
                return $match;
            }
        }
    }

    do_action('qm/error', 'Match ' . $matchID . ' from Sport ' . $sportID . ' not found');

    return '"Match Not Found"';
}

/**
 * Get odds based on parameters
 *
 * @param Array $gameInfo should be a return object from the getMatch() function
 * @param String $fixture represents the requested fixture (example 1, X or 2)
 *
 * @return String odds, eventually this will be made into float number
 */
function getMatchOdds($gameInfo, $fixture)
{
    $fixture = strtolower($fixture);
    if (!isset($gameInfo['Odds'])) {
        return "";
    }
    return match ($fixture) {
        "1" => floatval($gameInfo['Odds']['Home']),
        "2" => floatval($gameInfo['Odds']['Away']),
        "x" => isset($gameInfo['Odds']['Draw']) ? floatval($gameInfo['Odds']['Draw']) : '0', // For some type of matches draw is missing
        default => 0,
    };
}

/**
 * getOdds function that runs when the [getOdds] shortcode is called
 *
 * @param int $sportID takes the ID of the sport [1, 2, 8, 10] we are getting the match from
 * @param int $leagueID is optional but treat as required because helps fetching data from cache quicker
 * @param int $matchID takes the ID of the match we are searching for
 * @param String $fixture represents the requested fixture (example 1, X or 2)
 *
 * @return Float the fixed odds for a given match
 */
function getOdds(int|string $sportID, int|string $leagueID, int|string $matchID, $fixture)
{
    if (empty($matchID) || empty($fixture)) {
        if(DEBUG_MODE) {
            do_action('qm/error', 'getOdds -> Wrong Arguments');
        }
        return 'getOdds -> Wrong Arguments';
    }

    $gameInfo = getMatch($sportID, $matchID);
    if ($leagueID) {
        $gameInfo = getMatch($sportID, $matchID, $leagueID);
    }

    $odds = getMatchOdds($gameInfo, $fixture);

    if ($odds) {
        // Helper to translate 1, X and 2
        $outcomes = [
            "1" => 'Home',
            "X" => 'Draw',
            "2" => 'Away'
        ];

        if(DEBUG_MODE) {
            do_action('qm/info', 'getOdds -> Match: ' . $gameInfo['Home'] . ' - ' . $gameInfo['Away'] . '; Outcome: ' . $fixture . '; Odds: ' . $gameInfo['Odds'][$outcomes[$fixture]]);
        }

        return $odds;
    }

    return 0;
}