<?php
class ArlandAPI
{
    // Constants for better maintainability
    const TOKEN_REFRESH_INTERVAL = 540; // 9 minutes in seconds
    const MARKET_TYPE_1X2 = "1";
    const GAME_TYPE_NORMAL = "1";
    const DEFAULT_PAGE_SIZE = 1000;
    const NONCE_LENGTH = 32;

    protected static $_instance;
    private $url;
    private $endpoint;
    private $accessToken;
    private $accessTokenExpired;
    private $sessionInfo;
    private $refreshToken;
    private $tokenRefreshInterval;
    private $requestHeaders;
    public $authority;

    // Credentials which are used to get token
    private $clientIdForAuth;
    private $nonceForAuth;
    private $emailForAuth;
    private $secretKey;

    // Hashed version of the concatenated string (hashed by us after concat of clientId+Email+Nonce)
    private $signature;

    function __construct()
    {
        if(DEBUG_MODE) {
            do_action('qm/info', 'ArlandAPI initialized');
        }
    }

    /**
     * Helper method for transient operations with proper validation
     */
    private function getTransient($key, $unserialize = true)
    {
        $value = get_transient($key);
        if (!$value || $value === "null") {
            return null;
        }

        if ($unserialize) {
            return unserialize($value);
        }

        return $value;
    }

    /**
     * Helper method to validate tokens
     */
    private function isTokenValid($token)
    {
        return $token &&
               is_array($token) &&
               !empty($token['token']) &&
               (!isset($token['statusCode']) || $token['statusCode'] !== 403);
    }

    /**
     * Extract competitors from game name efficiently
     */
    private function extractCompetitors($gameName)
    {
        $parts = explode('-', $gameName, 2);
        return [
            'home' => trim($parts[0] ?? ''),
            'away' => trim($parts[1] ?? '')
        ];
    }

    /**
     * Static version for use in static methods
     */
    private static function extractCompetitorsStatic($gameName)
    {
        $parts = explode('-', $gameName, 2);
        return [
            'home' => trim($parts[0] ?? ''),
            'away' => trim($parts[1] ?? '')
        ];
    }

    /**
     * Check if all required odds are available
     */
    private static function hasValidOdds($market)
    {
        if (empty($market['odds']) || !is_array($market['odds']) || count($market['odds']) < 3) {
            return false;
        }

        foreach ($market['odds'] as $odd) {
            if (empty($odd['value'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Build bulk query string efficiently
     */
    private function buildBulkQuery($ids, $paramName = 'ids')
    {
        $params = [];
        foreach ($ids as $id) {
            $params[] = $paramName . '=' . urlencode($id);
        }
        return implode('&', $params);
    }

    private function prepareCredentials() {
        $this->setNonce(); // A random nonce number generated by us

        /* Prod Credentials Start */
        $this->url = 'https://nextapi.saber.systems/api';

        if ($this->authority === "SGA") {
            $this->clientIdForAuth = "con.sga.sweden.phoenix.q.api";
            $this->emailForAuth    = "<EMAIL>";
            $this->secretKey       = "BEE496D7-9926-41AE-A548-9CEB3A09D572";
        }

        if ($this->authority === "MGA") {
            $this->clientIdForAuth = "con.mga.phoenix.q.api";
            $this->emailForAuth    = "<EMAIL>";
            $this->secretKey       = "F518495B-94C8-4D15-9615-2FDB5DBD332D";
        }

        if ($this->authority === "GGA") {
            $this->clientIdForAuth = "con.gga.phoenix.opt.q.api";
            $this->emailForAuth    = "<EMAIL>";
            $this->secretKey       = "7AEBC585-F9D6-430D-AEC7-AF99BDD44D1A";
        }

        if ($this->authority === "DGA") {
            $this->clientIdForAuth = "con.dga.phoenix.opt.q.api";
            $this->emailForAuth    = "<EMAIL>";
            $this->secretKey       = "BF980F9C-7875-466F-B991-B61EEE69CDC2";
        }

        if ($this->authority === "PGA") {
            $this->clientIdForAuth = "con.pga.phoenix.opt.q.api";
            $this->emailForAuth    = "<EMAIL>";
            $this->secretKey       = "67DDFAF4-D49B-487E-B40D-C969B7FBBA82";
        }

        /* Prod Credentials End */

        if (LOCAL_ENV) {
            /* Staging Credentials Start */
            $this->url = 'https://nextapi.cleverdolphin.se/api';
            $this->clientIdForAuth = "con.mga.norway.q.api";
            $this->emailForAuth = "<EMAIL>";
            $this->secretKey = "B4BBB68C-0CAF-45E7-B5C5-6DA9893DC364";
            /* Staging Credentials End */
        }

        $this->tokenRefreshInterval = self::TOKEN_REFRESH_INTERVAL;
        $this->accessToken = $this->getTransient('px_api_matches_arland_access_token_' . $this->authority);
        $this->sessionInfo = $this->getTransient('px_api_matches_arland_session_' . $this->authority);
        $this->accessTokenExpired = false;

        $this->generateSignature();

        // Simplified token validation logic
        if (!$this->isTokenValid($this->accessToken)) {
            $this->refreshAuthCredentials();
        } else {
            // Check if token has error status
            if (isset($this->accessToken['statusCode']) && $this->accessToken['statusCode'] === 403) {
                $this->refreshAuthCredentials();
            } else {
                // Try to refresh token, but fall back to new token if refresh fails
                $this->getRefreshToken();

                // Check if refresh failed and fall back to new token
                if (isset($this->refreshToken['statusCode']) && $this->refreshToken['statusCode'] === 401) {
                    $this->refreshAuthCredentials();
                }
            }
        }

        if (!$this->isTokenValid($this->sessionInfo)) {
            $this->getsessionInfo();
        }
    }

    /**
     * Clear cached tokens when they become invalid
     */
    private function clearInvalidTokens()
    {
        delete_transient('px_api_matches_arland_access_token_' . $this->authority);
        delete_transient('px_api_matches_arland_refresh_token_' . $this->authority);
        delete_transient('px_api_matches_arland_session_' . $this->authority);
        delete_transient('px_api_matches_arland_auth_nonce_' . $this->authority);
    }

    /**
     * Consolidated method for refreshing authentication credentials
     */
    private function refreshAuthCredentials()
    {
        $this->accessTokenExpired = true;
        $this->clearInvalidTokens(); // Clear any cached invalid tokens
        $this->setNonce();
        $this->generateSignature();
        $this->requestHeaders = [
            "ClientId: $this->clientIdForAuth",
            "Nonce: $this->nonceForAuth",
            "Signature: $this->signature",
        ];
        $this->getAccessToken();
    }

    /**
     * Safe method to get authorization headers
     */
    private function getAuthHeaders($includeSession = true)
    {
        $headers = [];

        if (!empty($this->accessToken['token'])) {
            $headers[] = "Authorization: Bearer " . $this->accessToken['token'];
        }

        if ($includeSession && !empty($this->sessionInfo['sessionToken'])) {
            $headers[] = "Session: " . $this->sessionInfo['sessionToken'];
        }

        return $headers;
    }

    public static function init() {
        $api = ArlandAPI::getInstance();
        if ($api && $api->authority) {
            $api->prepareCredentials();
        }
    }

    public static function getInstance()
    {
        if (!isset(self::$_instance)) {
            self::$_instance = new ArlandAPI();
        }
        return self::$_instance;
    }

    private function setNonce()
    {
        $randomNonceString = bin2hex(random_bytes(self::NONCE_LENGTH));

        $this->nonceForAuth = $this->getTransient('px_api_matches_arland_auth_nonce_' . $this->authority, false);

        if (!$this->nonceForAuth || $this->accessTokenExpired) {
            $this->nonceForAuth = $randomNonceString;
            set_transient('px_api_matches_arland_auth_nonce_' . $this->authority, $this->nonceForAuth);
        }
    }

    private function generateSignature()
    {
        if (
            !empty($this->secretKey) &&
            !empty($this->emailForAuth) &&
            !empty($this->nonceForAuth) &&
            !empty($this->clientIdForAuth)
        ) {
            $this->signature = hash_hmac('sha256', $this->clientIdForAuth . $this->emailForAuth . $this->nonceForAuth, (string) $this->secretKey, true);
            $this->signature = strtoupper(bin2hex($this->signature));

            // do_action('qm/info', 'ArlandAPI->generateSignature() triggered');
        }
    }
    private function getAccessToken()
    {
        if ($this->accessTokenExpired) {
            $this->endpoint = "/auth/token";
            $curl = new Api($this->url . $this->endpoint, $this->requestHeaders);

            $response = $curl->get();
            $this->accessToken = $response;
            $this->accessTokenExpired = false;

            if(!empty($this->accessToken)) {
                set_transient('px_api_matches_arland_access_token_' . $this->authority, serialize($this->accessToken), self::TOKEN_REFRESH_INTERVAL);
            }

            // do_action('qm/info', 'ArlandAPI->getAccessToken() triggered');
        }
    }

    private function getRefreshToken()
    {
        if (empty($this->accessToken['token'])) {
            return;
        }

        // Always ensure we have the required auth headers for refresh token
        if (!is_array($this->requestHeaders)) {
            $this->setNonce();
            $this->generateSignature();
            $this->requestHeaders = [
                "ClientId: $this->clientIdForAuth",
                "Nonce: $this->nonceForAuth",
                "Signature: $this->signature",
            ];
        }

        $customHeader = $this->getAuthHeaders(true);

        // Merge with required auth headers for refresh token endpoint
        $customHeader = array_merge($customHeader, $this->requestHeaders);

        $this->endpoint = "/auth/refreshtoken";
        $curl = new Api($this->url . $this->endpoint, $customHeader);

        $response = $curl->put(['token' => $this->accessToken['token']]);
        $this->refreshToken = $response;

        // Only cache successful refresh tokens
        if ($response && (!isset($response['statusCode']) || $response['statusCode'] === 200)) {
            set_transient('px_api_matches_arland_refresh_token_' . $this->authority, serialize($response), self::TOKEN_REFRESH_INTERVAL);
        }

        // do_action('qm/info', 'ArlandAPI->getRefreshToken() triggered');
    }

    private function getsessionInfo() {
        if (is_array($this->accessToken)) {
            if (array_key_exists('token', $this->accessToken)) {
                $token = $this->accessToken['token'];
                $fields = [
                    "deviceIdentifier" => "Windows",
                    "userAgent" => "Chrome",
                    "ipAddress" => "127.0.0.1",
                ];
                $fields = json_encode($fields);

                $customHeader = [
                    "Authorization: Bearer $token",
                    "Content-Length: " . strlen($fields)
                ];

                $this->endpoint = "/session/create";
                $curl = new Api($this->url . $this->endpoint, $customHeader);
                $response = $curl->post($fields);

                $this->sessionInfo = $response;
                set_transient('px_api_matches_arland_session_' . $this->authority, serialize($response), self::TOKEN_REFRESH_INTERVAL);
            }
        }
    }

    public function getSports($languageId = "0")
    {
        if(is_string($this->accessToken)) return false;

        $customHeader = $this->getAuthHeaders(true);

        $dates = static::getDateForTodayAndNextMonth();
        $today = $dates['today'];
        $nextMonth = $dates['next_month'];

        $this->endpoint = "/data/sports/bettable/$today/$nextMonth/$languageId";
        $curl = new Api($this->url . $this->endpoint, $customHeader);
        $response = $curl->get();

        // do_action('qm/info', 'ArlandAPI->getSports() triggered');
        return $response;
    }

    private function getCategories($sportId = NULL, $languageId = "0")
    {
        if ($sportId === NULL) return;

        $customHeader = $this->getAuthHeaders(true);

        $dates = static::getDateForTodayAndNextMonth();
        $today = $dates['today'];
        $nextMonth = $dates['next_month'];

        // Get bettable categories
        $this->endpoint = "/data/categories/bettable/$sportId/$today/$nextMonth/$languageId";

        $curl = new Api($this->url . $this->endpoint, $customHeader);
        $response = $curl->get();

        return $response;
    }

    private function getLeagues($sportId = NULL, $gameCategoryId = NULL, $languageId = "0")
    {
        if ($sportId === NULL) return;

        $customHeader = $this->getAuthHeaders(true);

        $dates = static::getDateForTodayAndNextMonth();
        $today = $dates['today'];
        $nextMonth = $dates['next_month'];

        // Get all leagues
        // $this->endpoint = "/data/leagues/search/$languageId?sportId=$sportId";

        // Get bettable leagues only, also only the ones in the upcoming 30 days
        $this->endpoint = "/data/leagues/bettable/$sportId/$gameCategoryId/$today/$nextMonth/$languageId";

        $curl = new Api($this->url . $this->endpoint, $customHeader);
        $response = $curl->get();

        // do_action('qm/info', 'ArlandAPI->getLeagues() triggered');

        return $response;
    }

    public static function getCompetitors($gameID)
    {
        $api = ArlandAPI::getInstance();

        if (empty($api->accessToken['token'])) {
            return false;
        }

        $customHeader = $api->getAuthHeaders(true);

        // Get game competitors
        $api->endpoint = "/data/gamecompetitors?gameIds=" . $gameID;
        $curl = new Api($api->url . $api->endpoint, $customHeader);
        $response = $curl->get();

        if ($response && is_array($response) && count($response) >= 2) {
            return [
                'home' => $response[0]['name'] ?? '',
                'away' => $response[1]['name'] ?? ''
            ];
        }

        return $response;
    }

    /**
     * Optimized method to get all games for 1x2 market for a specific sport using direct API call
     * This reduces hundreds of API calls to just one call per sport
     *
     * @param int $sportId The sport ID to filter games for
     * @param string $languageId The language ID
     * @param int $pageSize Number of games per page (default: 1000)
     * @param int $pageNumber Page number for pagination (default: 1)
     * @return array|false Array of games or false on failure
     */
    public function getGamesForSportOptimized($sportId, $languageId = "0", $pageSize = 1000, $pageNumber = 1)
    {
        if(is_string($this->accessToken)) return false;

        $customHeader = $this->getAuthHeaders(true);

        $dates = static::getDateForTodayAndNextMonth();
        $today = $dates['today'];
        $nextMonth = $dates['next_month'];

        // Use the optimized endpoint that gets games directly by sport and market type
        $this->endpoint = "/data/games/$today/$nextMonth/$languageId?sportId=$sportId&marketTypeIds=" . self::MARKET_TYPE_1X2 . "&gameTypes=" . self::GAME_TYPE_NORMAL . "&pageSize=$pageSize&pageNumber=$pageNumber";

        $curl = new Api($this->url . $this->endpoint, $customHeader);
        $response = $curl->get();

        // do_action('qm/info', "ArlandAPI->getGamesForSportOptimized() called for sportId: $sportId");
        return $response;
    }

    /**
     * Get all games for a specific sport using pagination
     *
     * @param int $sportId The sport ID
     * @param string $languageId The language ID
     * @return array All games for the sport across all pages
     */
    public function getAllGamesForSport($sportId, $languageId = "0")
    {
        $allGames = [];
        $pageNumber = 1;
        $pageSize = self::DEFAULT_PAGE_SIZE;

        do {
            $response = $this->getGamesForSportOptimized($sportId, $languageId, $pageSize, $pageNumber);

            if (!$response || !is_array($response) || !array_key_exists('items', $response)) {
                break;
            }

            $games = $response['items'];
            $allGames = array_merge($allGames, $games);

            $pageNumber++;
        } while (count($games) == $pageSize);

        return $allGames;
    }

    /**
     * Fetch league names in bulk using league IDs
     * This method significantly reduces API calls by fetching multiple league names at once
     *
     * @param array $leagueIds Array of league IDs to fetch names for
     * @return array Associative array mapping league ID to league name
     */
    public function getLeagueNamesBulk($leagueIds)
    {
        if (empty($leagueIds) || !is_array($leagueIds)) {
            return [];
        }

        if(is_string($this->accessToken)) return [];

        $customHeader = $this->getAuthHeaders(true);

        // Build query string with multiple league IDs using helper method
        $idsQuery = $this->buildBulkQuery($leagueIds);

        $this->endpoint = "/data/leagues/ids?" . $idsQuery;

        if(DEBUG_MODE) {
            error_log($this->endpoint);
        }

        $curl = new Api($this->url . $this->endpoint, $customHeader);
        $response = $curl->get();

        $leagueNames = [];
        if ($response && is_array($response)) {
            foreach ($response as $league) {
                if (isset($league['id'], $league['name'])) {
                    $leagueNames[$league['id']] = $league['name'];
                }
            }
        }

        // do_action('qm/info', "ArlandAPI->getLeagueNamesBulk() fetched " . count($leagueNames) . " league names for " . count($leagueIds) . " IDs");

        return $leagueNames;
    }

    /**
     * Fetch game category (country) names in bulk using category IDs
     * Categories represent countries in the system
     *
     * @param array $categoryIds Array of category IDs to fetch names for
     * @return array Associative array mapping category ID to category/country name
     */
    public function getCategoryNamesBulk($categoryIds)
    {
        if (empty($categoryIds) || !is_array($categoryIds)) {
            return [];
        }

        if(is_string($this->accessToken)) return [];

        $customHeader = $this->getAuthHeaders(true);

        // Build query string with multiple category IDs using helper method
        $idsQuery = $this->buildBulkQuery($categoryIds);

        $this->endpoint = "/data/categories/ids?" . $idsQuery;

        if(DEBUG_MODE) {
            error_log($this->endpoint);
        }

        $curl = new Api($this->url . $this->endpoint, $customHeader);
        $response = $curl->get();

        $categoryNames = [];
        if ($response && is_array($response)) {
            foreach ($response as $category) {
                if (isset($category['id'], $category['name'])) {
                    $categoryNames[$category['id']] = $category['name'];
                }
            }
        }

        // do_action('qm/info', "ArlandAPI->getCategoryNamesBulk() fetched " . count($categoryNames) . " category names for " . count($categoryIds) . " IDs");

        return $categoryNames;
    }    public static function getMatches()
    {
        $api = ArlandAPI::getInstance();
        $api->fetch();
    }


    /**
     * Improved date handling using DateTime objects
     */
    public static function getDateForTodayAndNextMonth()
    {
        $today = new \DateTime();
        $nextMonth = clone $today;

        // Handle end of month edge cases more elegantly
        $nextMonth->add(new \DateInterval('P1M'));

        return [
            'today' => $today->format('Y-m-d') . 'T00:00:00Z',
            'next_month' => $nextMonth->format('Y-m-d') . 'T00:00:00Z'
        ];
    }

    public static function fetch($languageId = "0")
    {
        $api = ArlandAPI::getInstance();
        $sports = $api->getSports();
        $matchesTransient = [];

        if (!is_array($sports)) return false;

        // Get key/value pairs for 'id','name' keys only
        $sportsFiltered = array_reduce($sports, function ($carry, $item) {
            if(!empty($item['id'])) {
                $arr = [
                    'id' => $item['id'],
                    'name' => $item['name'],
                ];

                $carry[] = $arr;

                return $carry;
            }
        }, []);

        // Use optimized approach - get games directly for each sport
        $allLeagueIds = []; // Collect all unique league IDs
        $allCategoryIds = []; // Collect all unique category IDs (countries)
        $leagueToCategory = []; // Map league ID to category ID for country mapping

        foreach ($sportsFiltered as $sport) {
            $sportId = $sport['id'];

            if (!array_key_exists($sportId, $matchesTransient)) {
                $matchesTransient[$sportId] = ["SportName" => $sport['name'], "SportID" => $sportId];
            }

            // Get all games for this sport using the optimized method
            $allGames = $api->getAllGamesForSport($sportId, $languageId);

            if (empty($allGames)) {
                continue;
            }

            // Group games by league and collect league IDs and category IDs
            $gamesByLeague = [];
            foreach ($allGames as $game) {
                $gameId = $game['id'];
                $leagueId = $game['leagueId'];
                $categoryId = $game['gameCategoryId']; // This represents the country

                // Collect unique league IDs for bulk fetching
                if (!in_array($leagueId, $allLeagueIds)) {
                    $allLeagueIds[] = $leagueId;
                }

                // Collect unique category IDs (countries) for bulk fetching
                if (!in_array($categoryId, $allCategoryIds)) {
                    $allCategoryIds[] = $categoryId;
                }

                // Map league to category for later country name combining
                $leagueToCategory[$leagueId] = $categoryId;

                                // Find 1x2 market
                $market = [];
                foreach ($game['markets'] as $marketData) {
                    if ($marketData['marketTypeId'] == self::MARKET_TYPE_1X2) {
                        $market = array_merge($market, $marketData);
                        break;
                    }
                }

                // If 1x2 market not found, continue to next game
                if (empty($market)) continue;

                // Check if all odds are available using helper method
                if (!self::hasValidOdds($market)) {
                    continue;
                }

                // Extract competitors using static helper
                $competitors = self::extractCompetitorsStatic($game['name']);

                // Initialize league if not exists (without league name for now)
                if (!isset($gamesByLeague[$leagueId])) {
                    $gamesByLeague[$leagueId] = [
                        'LeagueName' => null, // Will be filled later
                        'games' => []
                    ];
                }

                $gamesByLeague[$leagueId]['games'][$gameId] = [
                    'SportID'  => $sportId,
                    'LeagueID' => $leagueId,
                    'GameID'   => $gameId,
                    'GameDate' => $game['startDate'],
                    'Home'     => $competitors['home'],
                    'Away'     => $competitors['away'],
                    'LineIDs'  => [
                        'Home' => $market['odds'][0]['id'],
                        'Draw' => $market['odds'][1]['id'],
                        'Away' => $market['odds'][2]['id']
                    ],
                    'Odds' => [
                        'Home' => $market['odds'][0]['value'],
                        'Draw' => $market['odds'][1]['value'],
                        'Away' => $market['odds'][2]['value'],
                    ]
                ];
            }

            // Add leagues with games to the matches transient (names will be added later)
            foreach ($gamesByLeague as $leagueId => $leagueData) {
                if (!empty($leagueData['games'])) {
                    $matchesTransient[$sportId][$leagueId] = [
                        'LeagueName' => null // Will be filled later
                    ];
                    // Directly merge the games using their original GameIDs as keys
                    foreach ($leagueData['games'] as $gameId => $gameData) {
                        $matchesTransient[$sportId][$leagueId][$gameId] = $gameData;
                    }
                }
            }
        }

        // Now fetch all league names and category names in bulk API calls
        if (!empty($allLeagueIds) && !empty($allCategoryIds)) {
            // do_action('qm/info', 'Fetching ' . count($allLeagueIds) . ' league names and ' . count($allCategoryIds) . ' category names');

            // Fetch league names and category names in parallel
            $leagueNames = $api->getLeagueNamesBulk($allLeagueIds);
            $categoryNames = $api->getCategoryNamesBulk($allCategoryIds);

            // Update all league names in the matches transient with "Country - League" format
            foreach ($matchesTransient as $sportId => &$sportData) {
                if (is_array($sportData)) {
                    foreach ($sportData as $leagueId => &$leagueData) {
                        if (is_array($leagueData) && array_key_exists('LeagueName', $leagueData)) {
                            $leagueName = isset($leagueNames[$leagueId]) ? $leagueNames[$leagueId] : "League $leagueId";
                            $categoryId = isset($leagueToCategory[$leagueId]) ? $leagueToCategory[$leagueId] : null;
                            $countryName = ($categoryId && isset($categoryNames[$categoryId])) ? $categoryNames[$categoryId] : '';

                            // Format as "Country - League" to match legacy pattern
                            if (!empty($countryName)) {
                                $leagueData['LeagueName'] = $countryName . ' - ' . $leagueName;
                            } else {
                                $leagueData['LeagueName'] = $leagueName;
                            }
                        }
                    }
                }
            }

            // do_action('qm/info', 'Successfully mapped league names with country prefixes to games data');
        }

        // Clean up sports that have no leagues/games (only SportName and SportID)
        foreach ($matchesTransient as $sportId => $sportData) {
            if (is_array($sportData) && count($sportData) === 2 &&
                isset($sportData['SportName']) && isset($sportData['SportID'])) {
                // do_action('qm/info', "Removing sport {$sportData['SportName']} (ID: $sportId) - no games found");
                unset($matchesTransient[$sportId]);
            }
        }        $matchesTransient[] = ['lastUpdatedAt' => (new \DateTime())->format('Y-m-d H:i:s')];

        set_transient('px_api_matches_arland_' . $api->authority, $matchesTransient);
    }

    /**
     * Legacy fetch method using the old hierarchical approach
     * Kept for comparison purposes
     */
    public static function fetchLegacy($languageId = "0")
    {
        $api = ArlandAPI::getInstance();
        $sports = $api->getSports();
        $matchesTransient = [];

        if (!is_array($sports)) return false;

        // Get key/value pairs for 'id','name' keys only
        $sportsFiltered = array_reduce($sports, function ($carry, $item) {
            if(!empty($item['id'])) {
                $arr = [
                    'id' => $item['id'],
                    'name' => $item['name'],
                ];

                $carry[] = $arr;

                return $carry;
            }
        }, []);

        // Get leagues for each sport (OLD HIERARCHICAL APPROACH)
        foreach ($sportsFiltered as $sport) {
            $sportId = $sport['id'];
            $bettableCategories = $api->getCategories($sportId);

            if (!is_array($bettableCategories)) continue;

            foreach ($bettableCategories as $category) {
                if (is_array($category)) {
                    if (array_key_exists('id', $category)) {
                        $gameCategoryId = $category['id'];
                        $gameCategoryName = $category['name'];
                        $leagues = $api->getLeagues($sportId, "$gameCategoryId");

                        if (!is_array($leagues)) {
                            continue;
                        }

                        foreach ($leagues as &$league) {
                            $league['name'] = $gameCategoryName . ' - ' . $league['name'];
                        }

                        $leaguesFiltered = array_reduce($leagues, function ($carry, $item) {
                            if (!empty ($item['id'])) {
                                $arr = [
                                    'id' => $item['id'],
                                    'name' => $item['name'],
                                ];

                                $carry[] = $arr;

                                return $carry;
                            }
                        }, []);

                        // FETCH MATCHES FOR LEAGUES AND SAVE
                        $token = $api->accessToken['token'];

                        $customHeader = [
                            "Authorization: Bearer $token",
                            "Session: " . $api->sessionInfo['sessionToken']
                        ];

                        foreach ($leaguesFiltered as $league) {

                            $leagueId = $league['id'];
                            if (!array_key_exists($sportId, $matchesTransient)) {
                                $matchesTransient[$sportId] = ["SportName" => $sport['name'], "SportID" => $sportId];
                            }

                            if (array_key_exists($sportId, $matchesTransient)) {
                                if (!array_key_exists($leagueId, $matchesTransient[$sportId])) {
                                    $matchesTransient[$sportId][$leagueId] = [];
                                    $matchesTransient[$sportId][$leagueId]['LeagueName'] = $league['name'];
                                }
                            }

                            $api = ArlandAPI::getInstance();
                            $api->endpoint = "/data/games/gamesbyleague/$leagueId/$languageId?gameTypes=1&marketTypeIds=1";
                            $curl = new Api($api->url . $api->endpoint, $customHeader);
                            $response = $curl->get();

                            if ($response) {
                                if (is_array($response)) {
                                    if (array_key_exists('items', $response)) {
                                        foreach ($response['items'] as $game) {
                                            $gameId = $game['id'];
                                            $market = [];
                                            foreach ($game['markets'] as $marketData) {
                                                if ($marketData['marketTypeId'] == self::MARKET_TYPE_1X2) {
                                                    $market = array_merge($market, $marketData);
                                                    break;
                                                }
                                            }

                                            // If 1x2 market not found, continue to next game
                                            if (empty($market)) continue;

                                            // Use improved odds validation
                                            if (!self::hasValidOdds($market)) {
                                                continue;
                                            }

                                            $competitors = self::extractCompetitorsStatic($game['name']);


                                            $matchesTransient[$sportId][$leagueId][$gameId] = [
                                                'SportID'  => $sportId,
                                                'LeagueID' => $leagueId,
                                                'GameID'   => $gameId,
                                                'GameDate' => $game['startDate'],
                                                'Home'     => $competitors['home'],
                                                'Away'     => $competitors['away'],
                                                'LineIDs'  => [
                                                    'Home' => $market['odds'][0]['id'],
                                                    'Draw' => $market['odds'][1]['id'],
                                                    'Away' => $market['odds'][2]['id']
                                                ],
                                                'Odds' => [
                                                    'Home' => $market['odds'][0]['value'],
                                                    'Draw' => $market['odds'][1]['value'],
                                                    'Away' => $market['odds'][2]['value'],
                                                ]
                                            ];
                                        }
                                    }
                                }
                            }

                            // If league has only 1 key, which is LeagueName, (if league has no matches), unset it
                            if(!empty($matchesTransient[$sportId][$leagueId])) {
                                if (count($matchesTransient[$sportId][$leagueId]) === 1) {
                                    unset($matchesTransient[$sportId][$leagueId]);
                                }
                            }
                        }
                    } else {
                        continue;
                    }
                }
            }

            // If sport has only 2 keys, which are SportID, SportName (if sport doesnt have any league), unset it
            if(!empty($sportId)) {
                if(!empty($matchesTransient[$sportId])) {
                    if (count($matchesTransient[$sportId]) === 2) {
                        unset($matchesTransient[$sportId]);
                    }
                }
            }
        }

        $matchesTransient[] = ['lastUpdatedAt' => (new \DateTime())->format('Y-m-d H:i:s')];

        set_transient('px_api_matches_arland_legacy_' . $api->authority, $matchesTransient);
    }
}