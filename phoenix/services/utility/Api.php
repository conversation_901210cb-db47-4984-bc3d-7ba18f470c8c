<?php
class Api
{
    private $curl;
    private $header;
    private $response;
    private $headerSize;
    private $responseHeaders;
    private $includeHeaders = false;

    public function __construct(private $url, $customHeaders = NULL, $queryString = NULL)
    {
        // Track API calls if performance testing is enabled
        if (class_exists('ApiCallCounter')) {
            ApiCallCounter::increment($this->url);
        }

        $this->curl = curl_init();
        $this->header = [
            "User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
            "cache-control: no-cache",
            "Content-Type: application/json"
        ];

        $curlOptions = [
            CURLOPT_URL => $this->url,
            CURLOPT_HEADER => 0,
            CURLOPT_TIMEOUT_MS => 30000, // 30 sec
            CURLOPT_ENCODING => "gzip,deflate",
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_AUTOREFERER    => true,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_TIMEOUT        => 30,
            CURLOPT_MAXREDIRS      => 10,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false
        ];

        if (NULL !== $customHeaders) {
            // Handle curl options passed in customHeaders
            foreach ($customHeaders as $key => $value) {
                if (defined($key)) {
                    $curlOptions[constant($key)] = $value;
                } else {
                    $this->header[] = $value;
                }
            }
        }

        if (NULL !== $queryString) {
            $this->url = $this->url . $queryString;
            $curlOptions[CURLOPT_URL] = $this->url;
        }

        $curlOptions[CURLOPT_HTTPHEADER] = $this->header;
        curl_setopt_array($this->curl, $curlOptions);
    }

    public function setAuth($userpwd)
    {
        array_push($this->header, "Authorization: Basic $userpwd");
        curl_setopt($this->curl, CURLOPT_HTTPHEADER, $this->header);
    }

    public function setHeaderCapture(bool $enabled = true): void
    {
        $this->includeHeaders = $enabled;
        curl_setopt($this->curl, CURLOPT_HEADER, $enabled);
    }

    public function get()
    {
        $this->response = curl_exec($this->curl);
        $info = curl_getinfo($this->curl);

        if(DEBUG_MODE) {
            do_action('qm/debug', ['Curl info:', $info]);
        }

        // Process headers only if explicitly enabled
        if ($this->includeHeaders && $info['header_size'] > 0) {
            $this->headerSize = $info['header_size'];
            $this->responseHeaders = substr($this->response, 0, $this->headerSize);
            $body = substr($this->response, $this->headerSize);

            if(DEBUG_MODE) {
                do_action('qm/debug', ['Response size:', strlen($this->response)]);
                do_action('qm/debug', ['Header size:', $this->headerSize]);
                do_action('qm/debug', ['Headers:', $this->responseHeaders]);
            }
        } else {
            $body = $this->response;
        }

        curl_close($this->curl);

        $this->test();
        $this->log();

        if (!$this->response) {
            return false;
        }

        $json = json_decode($body, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $json;
        }
        return $body;
    }

    public function getCookies(): array
    {
        if (empty($this->responseHeaders)) {
            if(DEBUG_MODE) {
                do_action('qm/debug', 'No response headers found');
            }
            return [];
        }

        $cookies = [];

        // Split multiple HTTP responses
        $responses = explode("\n\nHTTP/", $this->responseHeaders);

        foreach ($responses as $response) {
            if (!empty($response)) {
                // Add back the HTTP/ prefix except for the first response
                if (!str_starts_with($response, 'HTTP/')) {
                    $response = "HTTP/" . $response;
                }

                // Match the entire cookie line to preserve the full value
                if (preg_match_all('/^set-cookie:\s*([^=]+)=([^;]+)/mi', $response, $matches)) {
                    for ($i = 0; $i < count($matches[1]); $i++) {
                        $name = trim($matches[1][$i]);
                        $value = trim($matches[2][$i], ' "');
                        $cookies[$name] = $value;
                    }
                }
            }
        }

        if(DEBUG_MODE) {
            do_action('qm/debug', ['Raw headers:', $this->responseHeaders]);
            do_action('qm/debug', ['Parsed cookies:', $cookies]);
        }

        return $cookies;
    }

    public function getHeaders(): string
    {
        return $this->responseHeaders ?? '';
    }

    public function post(string $body)
    {
        curl_setopt_array($this->curl, [
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $body,
        ]);

        return $this->get();
    }

    public function put($data = NULL)
    {
        if (NULL !== $data) {
            curl_setopt_array($this->curl, [
                CURLOPT_CUSTOMREQUEST =>  "PUT",
                CURLOPT_POSTFIELDS => http_build_query($data)
            ]);
            return $this->get();
        }
    }

    public function cookie(string $cookie)
    {
        curl_setopt_array($this->curl, [
            CURLOPT_CUSTOMREQUEST =>  "GET",
            CURLOPT_COOKIE  => $cookie,
        ]);

        return $this->get();
    }

    public function getCurl()
    {
        return $this->curl;
    }

    private function test()
    {
        if (DEBUG_MODE || !empty($_GET['test_api'])) {
            do_action('qm/debug', curl_getinfo($this->curl));
            do_action('qm/debug', $this->response);
        }
    }

    private function log()
    {
        if (defined('API_LOG') && API_LOG) {
            $log  = "Datetime: " . date('Y-m-d H:i:s') . PHP_EOL .
                "CURL: " . json_encode(curl_getinfo($this->curl)) . PHP_EOL .
                "Api Response: " . $this->response . PHP_EOL;

            $filepath = WP_CONTENT_DIR . '/api.log';
            file_put_contents($filepath, $log, (file_exists($filepath) ? FILE_APPEND : 0));
        }
    }

    // public function useMockCookie()
    // {
    //     $mocker = function() {
    //         $currentGeoCode = strtoupper(CURRENT_REGION);

    //         $allowedMockGeoCodes = [
    //             'DE',
    //             'FI',
    //             'CA',
    //             'CA_ON',
    //             'PL',
    //             'DK',
    //             'IN',
    //             'JP',
    //             'NO',
    //             'NL',
    //         ];

    //         // For Ontario
    //         if($currentGeoCode === 'ON') {
    //             $currentGeoCode = 'CA_ON';
    //         }

    //         if(in_array($currentGeoCode, $allowedMockGeoCodes)){
    //             curl_setopt_array($this->curl, [
    //                 CURLOPT_COOKIE  => 'mockgeoip=' . $currentGeoCode . ';'
    //             ]);
    //         }

    //         do_action('qm/info', $this->url . ' fetched with Cookie: mockgeoip=' . $currentGeoCode);
    //     };

    //     global $apiBase;

    //     if(!empty( $apiBase[SITE_TYPE_DEV]) ) {
    //         if (str_contains($this->url, $apiBase[SITE_TYPE_DEV])) {
    //             $mocker();
    //         }
    //     }

    //     if(!empty( $apiBase[SITE_TYPE_STAGING]) ) {
    //         if (str_contains($this->url, $apiBase[SITE_TYPE_STAGING])) {
    //             $mocker();
    //         }
    //     }
    // }
}
