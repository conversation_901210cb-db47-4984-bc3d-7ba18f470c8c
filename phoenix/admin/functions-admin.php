<?php

require_once 'functions/utilities.php';
require_once 'functions/brand-settings-pages.php';
require_once 'functions/clean-menu.php';
require_once 'functions/topbar-menu.php';
require_once 'functions/release-note.php';
require_once 'functions/duplicate-post.php';
require_once 'functions/disable-emoji.php';
require_once 'functions/tinymce.php';
require_once 'functions/upload-files.php';
require_once 'functions/quiz-questions-count.php';
require_once 'functions/matchup.php';
require_once 'functions/help-boxes.php';
require_once 'functions/timezone-helper.php';
require_once 'functions/gutenberg-blocks.php';
require_once 'functions/acf-block-validation.php';
require_once 'functions/acf-fields.php';
require_once 'functions/acf-validation.php';
require_once 'functions/admin-columns.php';
require_once 'functions/quick-edit-fields.php';
require_once 'functions/enqueue-scripts.php';
require_once 'functions/menu-gtm-tracking.php';

// Include it only if user is on wordpress dashboard
if(is_admin()) {
    require_once 'functions/toolkit/arland-api-test-page.php';
}

// Hide the admin bar on FE
add_filter('show_admin_bar', '__return_false');

// Admin init extras
add_action('admin_init', 'admin_init_extras');
function admin_init_extras() {
    // Proxy URLs workaround
    $proxyType = '';

    if(SITE_TYPE == SITE_TYPE_GO) {
        $proxyType = '/go';
    } elseif(SITE_TYPE == SITE_TYPE_RG) {
        $proxyType = '/rg';
    } elseif(SITE_TYPE == SITE_TYPE_FAQ) {
        $proxyType = '/faq';
    }

    if($proxyType) {
        $_SERVER['REQUEST_URI'] = str_replace('/wp-admin/', $proxyType . '/wp-admin/',  (string) $_SERVER['REQUEST_URI']);
    }

    // Proxy sites admin canonical URL fix
    remove_action( 'admin_head', 'wp_admin_canonical_url' );

    // Disable autosave
    wp_deregister_script('autosave');
}

// Amend user role capabilities
function px_user_caps() {
    // Contributor
    $contributor = get_role('contributor');
    $contributor->add_cap('edit_pages');
    $contributor->remove_cap('publish_posts');

    // Editor
    $editor = get_role('editor');
    $editor->add_cap('publish_posts');
    $editor->add_cap('view_site_health_checks');
    $editor->add_cap('wpseo_bulk_edit');
    $editor->add_cap('wpseo_edit_advanced_metadata');
    $editor->add_cap('wpseo_manage_options');
    $editor->add_cap('wpseo_manage_redirects');
}
add_action('admin_init', 'px_user_caps');

add_filter('admin_body_class', 'add_admin_body_classes');
function add_admin_body_classes($classes) {
    global $current_user;
    foreach ($current_user->roles as $role) {
        $classes .= ' user-role__' . $role;
    }
    return $classes;
}

// Add favicon to admin area
function add_favicon_to_admin() {
    echo '<link rel="icon" href="' . get_stylesheet_directory_uri() . '/dist/images/favicon/favicon.png">';
}
add_action('admin_head', 'add_favicon_to_admin');
add_action('login_head', 'add_favicon_to_admin');

// Add AJAX handler for random media image, to be used for ACF Autofill
if(DEV_ENV || LOCAL_ENV || STAGING_ENV) {
    add_action('wp_ajax_get_random_media_image', 'get_random_media_image');
}

function get_random_media_image() {
    // Verify nonce
    if (!check_ajax_referer('acf_autofill_nonce', 'nonce', false)) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    // Get random image from media library
    $args = array(
        'post_type' => 'attachment',
        'post_mime_type' => 'image',
        'post_status' => 'inherit',
        'posts_per_page' => 1,
        'orderby' => 'rand'
    );

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        $query->the_post();
        $image_data = array(
            'id' => get_the_ID(),
            'url' => wp_get_attachment_url(get_the_ID())
        );
        wp_send_json_success($image_data);
    } else {
        wp_send_json_error('No images found');
    }
    wp_reset_postdata();
}

// Remove update Wordpress notice on prod (ex: WordPress 6.4.2 is available! Please update now.)
if(!DEV_ENV){
    add_action('admin_head', function() {
        remove_action('admin_notices', 'update_nag', 3);
    });
}

// Instead of WordPress Version, show release version and confluence link on Dashboard footer
add_filter('update_footer', function() {
    return 'Version ' . RELEASE_VERSION . ' | <a href="https://comeon.atlassian.net/wiki/spaces/PHNX/overview" target="_blank">Knowledge Base</a>';
}, 11);
