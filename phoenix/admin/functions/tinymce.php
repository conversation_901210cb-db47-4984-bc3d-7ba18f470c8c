<?php
// tinymce functions to include tinymce table plugins
function add_the_table_button_to_tinymce( $buttons ) {
    array_push( $buttons, 'separator', 'table' );
    return $buttons;
 }
 add_filter( 'mce_buttons', 'add_the_table_button_to_tinymce' );

 function add_the_table_plugin_to_tinymce( $plugins ) {
     $plugins['table'] = get_template_directory_uri() . '/admin/scripts/vendor/tinymce/plugins/table/plugin.min.js';
     return $plugins;
 }
 add_filter( 'mce_external_plugins', 'add_the_table_plugin_to_tinymce' );

// Shortcode buttons added to tinymce
function add_tinymce_buttons($buttons)
{
    array_push($buttons, 'card_shortcode', 'button_shortcode', 'dropdown_shortcode', 'dynamic_content_shortcode', 'player_api_shortcode');

    if (isFeatureActive('api/jackpots')) {
        array_push($buttons, 'getjackpot_shortcode');
    }

    if (isFeatureActive('api/sportsbook')) {
        array_push($buttons, 'getodds_shortcode');
    }

    return $buttons;
}
add_filter('mce_buttons', 'add_tinymce_buttons');

function add_tinymce_buttons_js( $plugin_array ) {
    $plugin_array['card_shortcode'] = get_template_directory_uri().'/admin/shortcodes/card.js';
    $plugin_array['button_shortcode'] = get_template_directory_uri().'/admin/shortcodes/button.js';
    $plugin_array['dropdown_shortcode'] = get_template_directory_uri().'/admin/shortcodes/dropdown.js';
    $plugin_array['dynamic_content_shortcode'] = get_template_directory_uri().'/admin/shortcodes/dynamic-content.js';
    $plugin_array['player_api_shortcode'] = get_template_directory_uri().'/admin/shortcodes/player-api.js';

    if (isFeatureActive('api/jackpots')) {
        $plugin_array['getjackpot_shortcode'] = get_template_directory_uri().'/admin/shortcodes/getjackpot.js';
    }

    if (isFeatureActive('api/sportsbook')) {
        $plugin_array['getodds_shortcode'] = get_template_directory_uri().'/admin/shortcodes/getodds.js';
    }

    return $plugin_array;
}
add_filter( 'mce_external_plugins', 'add_tinymce_buttons_js' );

function custom_tinymce_content_filter($init) {                                                                                                              // Disallow style attribute
    $init['paste_as_text']          = true;
    $init['convert_fonts_to_spans'] = true;

    // Ensure cleanup settings are enabled
    $init['verify_html']        = true;
    $init['cleanup']            = true;
    $init['cleanup_on_startup'] = true;

    return $init;
}
add_filter('tiny_mce_before_init', 'custom_tinymce_content_filter');
