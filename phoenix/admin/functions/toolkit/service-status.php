<?php
// Service Status

// Matches API
$admin_bar->add_node([
    'parent' => 'phoenix-toolkit',
    'id' => 'status-api-matches',
    'title' => '<span class="ab-icon dashicons dashicons-marker"></span> Matches API',
    'href' => '#',
    'meta' => [
        'class' => (
            (
                !empty(get_transient('px_api_matches_arland_' . CURRENT_AUTHORITY))
            ) ? 'success' : 'error'
        ),
        'title' => __('Matches API')
    ]
]);

// Jackpots API
$admin_bar->add_node([
    'parent' => 'phoenix-toolkit',
    'id' => 'status-api-jackpots',
    'title' => '<span class="ab-icon dashicons dashicons-marker"></span> Jackpots API',
    'href' => '#',
    'meta' => [
        'class' => ((!empty(get_transient('px_api_jackpotsPNG_'. CURRENT_CURRENCY)) || !empty(get_transient('px_api_jackpotsEVO_'. CURRENT_CURRENCY))) ? 'success' : 'error'),
        'title' => __('Jackpots API')
    ]
]);