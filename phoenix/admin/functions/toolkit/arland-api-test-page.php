<?php
/**
 * ArlandAPI Performance Test Admin Page
 * This page compares the optimized vs legacy approach for fetching game data
 * Only accessible to WordPress administrators
 */

// Security check - ensure this is only accessible to WordPress admins
if (!defined('ABSPATH') || !current_user_can('administrator')) {
    return new WP_Error('403', 'Access denied for testing Arland API Performance.');
}

// API Call Counter class
if (!class_exists('ApiCallCounter')) {
    class ApiCallCounter {
        private static $count = 0;
        private static $calls = [];

        public static function increment($url) {
            self::$count++;
            self::$calls[] = [
                'call' => self::$count,
                'endpoint' => $url,
                'timestamp' => microtime(true)
            ];
        }

        public static function reset() {
            self::$count = 0;
            self::$calls = [];
        }

        public static function getCount() {
            return self::$count;
        }

        public static function getCalls() {
            return self::$calls;
        }
    }
}

// Add WordPress admin page functionality
add_action('admin_menu', 'arland_api_test_admin_menu');

function arland_api_test_admin_menu() {
    add_submenu_page(
        null, // Hidden from menu - no parent menu
        'ArlandAPI Performance Test',
        'ArlandAPI Test',
        'administrator',
        'arland-api-performance-test',
        'arland_api_test_admin_page'
    );
}

function arland_api_test_admin_page() {
    // Include global variables if needed
    if (!defined('LOCAL_ENV')) {
        require_once get_template_directory() . '/global-variables.php';
    }

    // Include the required classes
    require_once get_template_directory() . '/services/utility/Api.php';
    require_once get_template_directory() . '/services/sportsbook/providers/api/ArlandAPI.php';
    ?>

    <div class="wrap">
        <h1>🚀 ArlandAPI Performance Test</h1>

        <style>
            .performance-test-container {
                font-family: Arial, sans-serif;
                max-width: 1400px;
                margin: 20px 0;
            }
            .test-container {
                background: white;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                border: 1px solid #ccd0d4;
                overflow: hidden;
                min-width: 0;
            }
            .test-button {
                background: #0073aa;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin: 10px 10px 10px 0;
                font-size: 16px;
            }
            .test-button:hover {
                background: #005a87;
            }
            .test-button:disabled {
                background: #ccc;
                cursor: not-allowed;
            }
            .results {
                background: #f9f9f9;
                padding: 15px;
                border-radius: 4px;
                margin-top: 15px;
                overflow: hidden;
                min-width: 0;
            }
            .metric {
                display: inline-block;
                background: #e7f3ff;
                padding: 10px;
                margin: 5px;
                border-radius: 4px;
                border-left: 4px solid #0073aa;
            }
            .metric.improved {
                background: #e7f8e7;
                border-left-color: #28a745;
            }
            .metric.worse {
                background: #fce7e7;
                border-left-color: #dc3545;
            }
            .api-calls {
                max-height: 300px;
                overflow-y: auto;
                overflow-x: hidden;
                background: #f8f9fa;
                padding: 10px;
                border-radius: 4px;
                margin-top: 10px;
                width: 100%;
                box-sizing: border-box;
                word-wrap: break-word;
                word-break: break-all;
                white-space: pre-wrap;
            }
            .api-calls div {
                margin-bottom: 5px;
                line-height: 1.4;
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-family: monospace;
                font-size: 12px;
            }
            .api-calls div:hover {
                white-space: pre-wrap;
                text-overflow: unset;
                word-break: break-all;
                overflow-wrap: anywhere;
            }
            details {
                width: 100%;
                box-sizing: border-box;
                overflow: hidden;
                min-width: 0;
            }
            details summary {
                outline: none;
                user-select: none;
            }
            details[open] {
                overflow: visible;
            }
            .loading {
                display: none;
                color: #666;
                font-style: italic;
            }
            .comparison {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-top: 20px;
            }
            .progress {
                width: 100%;
                height: 20px;
                background: #f0f0f0;
                border-radius: 10px;
                overflow: hidden;
                margin: 10px 0;
            }
            .progress-bar {
                height: 100%;
                background: #0073aa;
                width: 0%;
                transition: width 0.3s ease;
            }
        </style>

        <div class="performance-test-container">
            <div class="test-container">
                <p>This tool compares the performance between the optimized and legacy approaches for fetching 1x2 market games.</p>

                <div>
                    <button class="test-button" onclick="runOptimizedTest()">Test Optimized Approach</button>
                    <button class="test-button" onclick="runLegacyTest()">Test Legacy Approach</button>
                    <button class="test-button" onclick="runComparison()" style="background: #28a745;">Run Full Comparison</button>
                    <button class="test-button" onclick="clearResults()" style="background: #6c757d;">Clear Results</button>
                </div>

                <div class="loading" id="loading">
                    <div class="progress">
                        <div class="progress-bar" id="progressBar"></div>
                    </div>
                    Running tests... <span id="loadingText">Initializing...</span>
                </div>
            </div>

            <div class="comparison">
                <div class="test-container">
                    <h2>📈 Optimized Approach</h2>
                    <div id="optimizedResults">No test run yet</div>
                </div>

                <div class="test-container">
                    <h2>📊 Legacy Approach</h2>
                    <div id="legacyResults">No test run yet</div>
                </div>
            </div>

            <div class="test-container">
                <h2>🔬 Performance Comparison</h2>
                <div id="comparisonResults">Run both tests to see comparison</div>
            </div>
        </div>

        <script>
            // WordPress AJAX URL (check if already defined)
            const arlandAjaxUrl = '<?php echo admin_url('admin-ajax.php'); ?>';

            let optimizedData = null;
            let legacyData = null;

            function updateProgress(percent, text) {
                document.getElementById('progressBar').style.width = percent + '%';
                document.getElementById('loadingText').textContent = text;
            }

            function showLoading() {
                document.getElementById('loading').style.display = 'block';
                updateProgress(0, 'Initializing...');
            }

            function hideLoading() {
                document.getElementById('loading').style.display = 'none';
            }

            async function runOptimizedTest() {
                showLoading();
                updateProgress(10, 'Setting up ArlandAPI...');

                try {
                    const response = await fetch(arlandAjaxUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=arland_api_test_optimized&_ajax_nonce=' + '<?php echo wp_create_nonce('arland_api_test'); ?>'
                    });

                    updateProgress(80, 'Processing results...');

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const text = await response.text();
                    let data;
                    try {
                        data = JSON.parse(text);
                    } catch (e) {
                        throw new Error(`Invalid JSON response: ${text.substring(0, 100)}...`);
                    }

                    updateProgress(100, 'Complete!');

                    optimizedData = data;
                    displayResults('optimizedResults', data, 'Optimized');

                    if (legacyData) {
                        updateComparison();
                    }
                } catch (error) {
                    document.getElementById('optimizedResults').innerHTML =
                        '<div style="color: red;">Error: ' + error.message + '</div>';
                }

                hideLoading();
            }

            async function runLegacyTest() {
                showLoading();
                updateProgress(10, 'Setting up ArlandAPI...');

                try {
                    const response = await fetch(arlandAjaxUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=arland_api_test_legacy&_ajax_nonce=' + '<?php echo wp_create_nonce('arland_api_test'); ?>'
                    });

                    updateProgress(80, 'Processing results...');

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const text = await response.text();
                    let data;
                    try {
                        data = JSON.parse(text);
                    } catch (e) {
                        throw new Error(`Invalid JSON response: ${text.substring(0, 100)}...`);
                    }

                    updateProgress(100, 'Complete!');

                    legacyData = data;
                    displayResults('legacyResults', data, 'Legacy');

                    if (optimizedData) {
                        updateComparison();
                    }
                } catch (error) {
                    document.getElementById('legacyResults').innerHTML =
                        '<div style="color: red;">Error: ' + error.message + '</div>';
                }

                hideLoading();
            }

            async function runComparison() {
                showLoading();
                updateProgress(5, 'Starting optimized test...');

                try {
                    // Run optimized test
                    const optimizedResponse = await fetch(arlandAjaxUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=arland_api_test_optimized&_ajax_nonce=' + '<?php echo wp_create_nonce('arland_api_test'); ?>'
                    });

                    updateProgress(35, 'Optimized test complete, starting legacy test...');

                    if (!optimizedResponse.ok) {
                        throw new Error(`HTTP error! status: ${optimizedResponse.status}`);
                    }

                    const optimizedText = await optimizedResponse.text();
                    try {
                        optimizedData = JSON.parse(optimizedText);
                    } catch (e) {
                        throw new Error(`Invalid JSON response: ${optimizedText.substring(0, 100)}...`);
                    }

                    displayResults('optimizedResults', optimizedData, 'Optimized');

                    // Run legacy test
                    const legacyResponse = await fetch(arlandAjaxUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=arland_api_test_legacy&_ajax_nonce=' + '<?php echo wp_create_nonce('arland_api_test'); ?>'
                    });

                    updateProgress(80, 'Processing comparison...');

                    if (!legacyResponse.ok) {
                        throw new Error(`HTTP error! status: ${legacyResponse.status}`);
                    }

                    const legacyText = await legacyResponse.text();
                    try {
                        legacyData = JSON.parse(legacyText);
                    } catch (e) {
                        throw new Error(`Invalid JSON response: ${legacyText.substring(0, 100)}...`);
                    }

                    displayResults('legacyResults', legacyData, 'Legacy');

                    updateProgress(100, 'Comparison complete!');
                    updateComparison();

                } catch (error) {
                    document.getElementById('comparisonResults').innerHTML =
                        '<div style="color: red;">Error: ' + error.message + '</div>';
                }

                hideLoading();
            }

            function displayResults(elementId, data, type) {
                const element = document.getElementById(elementId);

                if (data.error) {
                    element.innerHTML = '<div style="color: red;">Error: ' + data.error + '</div>';
                    return;
                }

                const html = `
                    <div class="results">
                        <div class="metric">
                            <strong>⏱️ Execution Time:</strong><br>
                            ${data.executionTime.toFixed(2)} seconds
                        </div>
                        <div class="metric">
                            <strong>📞 API Calls:</strong><br>
                            ${data.apiCallCount} calls
                        </div>
                        <div class="metric">
                            <strong>🎮 Games Found:</strong><br>
                            ${data.gamesCount} games
                        </div>
                        <div class="metric">
                            <strong>🏆 Sports Found:</strong><br>
                            ${data.sportsCount} sports
                        </div>

                        <details style="margin-top: 15px;">
                            <summary style="cursor: pointer; font-weight: bold;">📋 API Call Details</summary>
                            <div class="api-calls">
                                ${data.apiCalls.map((call, index) =>
                                    `<div><strong>#${call.call}:</strong> ${call.endpoint}</div>`
                                ).join('')}
                            </div>
                        </details>
                    </div>
                `;

                element.innerHTML = html;
            }

            function updateComparison() {
                if (!optimizedData || !legacyData) return;

                const timeImprovement = ((legacyData.executionTime - optimizedData.executionTime) / legacyData.executionTime * 100);
                const callReduction = ((legacyData.apiCallCount - optimizedData.apiCallCount) / legacyData.apiCallCount * 100);

                const html = `
                    <div class="results">
                        <h3>📊 Performance Metrics</h3>

                        <div class="metric ${timeImprovement > 0 ? 'improved' : 'worse'}">
                            <strong>⚡ Speed Improvement:</strong><br>
                            ${timeImprovement > 0 ? '+' : ''}${timeImprovement.toFixed(1)}%<br>
                            <small>${optimizedData.executionTime.toFixed(2)}s vs ${legacyData.executionTime.toFixed(2)}s</small>
                        </div>

                        <div class="metric ${callReduction > 0 ? 'improved' : 'worse'}">
                            <strong>📞 API Call Reduction:</strong><br>
                            ${callReduction > 0 ? '-' : '+'}${Math.abs(callReduction).toFixed(1)}%<br>
                            <small>${optimizedData.apiCallCount} vs ${legacyData.apiCallCount} calls</small>
                        </div>

                        <div class="metric">
                            <strong>📈 Efficiency Ratio:</strong><br>
                            ${(legacyData.apiCallCount / optimizedData.apiCallCount).toFixed(1)}x fewer calls
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 4px;">
                            <h4>🎯 Summary</h4>
                            <p>The optimized approach is <strong>${timeImprovement.toFixed(1)}% faster</strong> and uses <strong>${callReduction.toFixed(1)}% fewer API calls</strong> compared to the legacy hierarchical approach.</p>
                            <p>This represents a <strong>${(legacyData.apiCallCount / optimizedData.apiCallCount).toFixed(1)}x improvement</strong> in API efficiency.</p>
                        </div>
                    </div>
                `;

                document.getElementById('comparisonResults').innerHTML = html;
            }

            function clearResults() {
                document.getElementById('optimizedResults').innerHTML = 'No test run yet';
                document.getElementById('legacyResults').innerHTML = 'No test run yet';
                document.getElementById('comparisonResults').innerHTML = 'Run both tests to see comparison';
                optimizedData = null;
                legacyData = null;
            }
        </script>
    </div>

    <?php
}

// Add AJAX handlers for WordPress admin
add_action('wp_ajax_arland_api_test_optimized', 'handle_arland_api_test_optimized');
add_action('wp_ajax_arland_api_test_legacy', 'handle_arland_api_test_legacy');

function handle_arland_api_test_optimized() {
    if (!current_user_can('administrator')) {
        wp_die('Access denied');
    }

    // Verify nonce (check both POST and GET)
    $nonce = $_POST['_ajax_nonce'] ?? $_GET['_ajax_nonce'] ?? '';
    if (!wp_verify_nonce($nonce, 'arland_api_test')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Include required classes and handle the test
    handle_api_test('optimized');
}

function handle_arland_api_test_legacy() {
    if (!current_user_can('administrator')) {
        wp_die('Access denied');
    }

    // Verify nonce (check both POST and GET)
    $nonce = $_POST['_ajax_nonce'] ?? $_GET['_ajax_nonce'] ?? '';
    if (!wp_verify_nonce($nonce, 'arland_api_test')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Include required classes and handle the test
    handle_api_test('legacy');
}

function handle_api_test($method) {
    // Include global variables if needed
    if (!defined('LOCAL_ENV')) {
        require_once get_template_directory() . '/global-variables.php';
    }

    // Include the required classes
    require_once get_template_directory() . '/services/utility/Api.php';
    require_once get_template_directory() . '/services/sportsbook/providers/api/ArlandAPI.php';

    try {
        // Initialize ArlandAPI
        if (!class_exists('ArlandAPI')) {
            throw new Exception('ArlandAPI class not found');
        }

        $api = ArlandAPI::getInstance();
        $api->authority = 'MGA';
        ArlandAPI::init();

        if ($method === 'optimized') {
            ApiCallCounter::reset();
            $startTime = microtime(true);

            $sports = $api->getSports('0');
            $sportsDebug = [
                'sports_raw' => $sports,
                'sports_type' => gettype($sports),
                'sports_count' => is_array($sports) ? count($sports) : 0,
                'sports_is_string' => is_string($sports)
            ];

            ArlandAPI::fetch('0');

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;

            $transientData = get_transient('px_api_matches_arland_' . $api->authority);
            $gamesCount = 0;
            $sportsCount = 0;

            if ($transientData && is_array($transientData)) {
                foreach ($transientData as $sportId => $sportData) {
                    if (is_array($sportData) && isset($sportData['SportID'])) {
                        $sportsCount++;
                        foreach ($sportData as $leagueId => $leagueData) {
                            if (is_numeric($leagueId) && is_array($leagueData)) {
                                foreach ($leagueData as $gameId => $gameData) {
                                    if (is_numeric($gameId)) {
                                        $gamesCount++;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            wp_send_json([
                'success' => true,
                'executionTime' => $executionTime,
                'apiCallCount' => ApiCallCounter::getCount(),
                'apiCalls' => ApiCallCounter::getCalls(),
                'gamesCount' => $gamesCount,
                'sportsCount' => $sportsCount,
                'method' => 'optimized'
            ]);

        } else {
            ApiCallCounter::reset();
            $startTime = microtime(true);

            ArlandAPI::fetchLegacy('0');

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;

            $transientData = get_transient('px_api_matches_arland_legacy_' . $api->authority);
            $gamesCount = 0;
            $sportsCount = 0;

            if ($transientData && is_array($transientData)) {
                foreach ($transientData as $sportId => $sportData) {
                    if (is_array($sportData) && isset($sportData['SportID'])) {
                        $sportsCount++;
                        foreach ($sportData as $leagueId => $leagueData) {
                            if (is_numeric($leagueId) && is_array($leagueData)) {
                                foreach ($leagueData as $gameId => $gameData) {
                                    if (is_numeric($gameId)) {
                                        $gamesCount++;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            wp_send_json([
                'success' => true,
                'executionTime' => $executionTime,
                'apiCallCount' => ApiCallCounter::getCount(),
                'apiCalls' => ApiCallCounter::getCalls(),
                'gamesCount' => $gamesCount,
                'sportsCount' => $sportsCount,
                'method' => 'legacy'
            ]);
        }

    } catch (Exception $e) {
        wp_send_json([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
