<?php
/**
 * Add GTM Tracking Value field to WordPress menu items
 * This allows setting custom GTM tracking values for menu items instead of relying on hardcoded logic
 */

// Add custom field to menu item edit form
add_action('wp_nav_menu_item_custom_fields', 'add_gtm_tracking_field_to_menu', 10, 4);

function add_gtm_tracking_field_to_menu($item_id, $item, $depth, $args) {
    // Get the current GTM tracking value
    $gtm_tracking_value = get_post_meta($item_id, '_menu_item_gtm_tracking', true);
    ?>
    <p class="field-gtm-tracking description description-wide">
        <label for="edit-menu-item-gtm-tracking-<?php echo $item_id; ?>">
            GTM Tracking Value<br />
            <input type="text"
                   id="edit-menu-item-gtm-tracking-<?php echo $item_id; ?>"
                   class="widefat code edit-menu-item-gtm-tracking"
                   name="menu-item-gtm-tracking[<?php echo $item_id; ?>]"
                   value="<?php echo esc_attr($gtm_tracking_value); ?>"
                   placeholder="e.g., sportsbook, casino, live-casino" />
            <span class="description">Custom GTM tracking value for this menu item. If empty, will use automatic detection based on URL and text.</span>
        </label>
    </p>
    <?php
}

// Save the custom field value
add_action('wp_update_nav_menu_item', 'save_gtm_tracking_field_value', 10, 3);

function save_gtm_tracking_field_value($menu_id, $menu_item_db_id, $args) {
    // Check if our custom field was submitted
    if (isset($_POST['menu-item-gtm-tracking'][$menu_item_db_id])) {
        $gtm_tracking_value = sanitize_text_field($_POST['menu-item-gtm-tracking'][$menu_item_db_id]);

        // Save the value as post meta
        if (!empty($gtm_tracking_value)) {
            update_post_meta($menu_item_db_id, '_menu_item_gtm_tracking', $gtm_tracking_value);
        } else {
            // Delete the meta if empty
            delete_post_meta($menu_item_db_id, '_menu_item_gtm_tracking');
        }
    }
}

// Add the GTM tracking value to menu items when they are retrieved
add_filter('wp_get_nav_menu_items', 'add_gtm_tracking_to_menu_items', 10, 3);

function add_gtm_tracking_to_menu_items($items, $menu, $args) {
    foreach ($items as $item) {
        $gtm_tracking_value = get_post_meta($item->ID, '_menu_item_gtm_tracking', true);
        $item->gtm_tracking = $gtm_tracking_value;
    }
    return $items;
}
