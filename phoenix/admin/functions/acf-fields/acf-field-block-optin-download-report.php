<?php

class acf_field_block_optin_download_report extends acf_field {

	public function __construct() {
		// vars
		$this->name     = 'block_optin_download_report';
		$this->label    = __('Optin Block Donload Report');
		$this->category = __('Phoenix');

		// do not delete!
		parent::__construct();

		define('BLOCK_OPTIN_TABLE_NAME', 'px_block_optin');

		// Create BTE table in database
		add_action('after_switch_theme', function () {
			global $wpdb;
			$charset_collate = $wpdb->get_charset_collate();
			$sql = "CREATE TABLE IF NOT EXISTS " . BLOCK_OPTIN_TABLE_NAME . " (
				id int(12) NOT NULL AUTO_INCREMENT,
				post_id int(12) NOT NULL,
				report_id VARCHAR(32) NOT NULL,
				email VARCHAR(100) NOT NULL,
				data TEXT NOT NULL,
				time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
				PRIMARY KEY (id)
			) $charset_collate;";

			$wpdb->query($sql);
		});

		add_action('admin_init', [$this, 'generateBlockOptinReport'], 1, 0);
	}

	public function render_field($field) {
		global $wpdb, $post;

		if (empty($field['value'])) {
			$field['value'] = md5(microtime());
		}

		$count = $wpdb->get_var("SELECT COUNT(*) FROM `" . BLOCK_OPTIN_TABLE_NAME . "` WHERE `post_id` = {$post->ID} AND `report_id` = '{$field['value']}'");
?>
		<div>Entries: <strong><?= $count ?: 0 ?></strong></div>
		<br />
		<a target="_blank" download href="<?= add_query_arg(['download' => 'report', 'report_id' => $field['value']], get_edit_post_link($post->ID)) ?>" class="button-primary acf-button">Download Report</a>
		<br /><br /><br />
		<div><b>report ID</b></div><br />
		<input type="text" value="<?php echo esc_attr($field['value']); ?>" name="<?php echo esc_attr($field['name']); ?>" />
<?php
	}

	public function generateBlockOptinReport() {
		global $pagenow;
		if (is_admin() && $pagenow == 'post.php' && isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['report_id']) && isset($_GET['download']) && $_GET['download'] == 'report') {
			$reportID = sanitize_text_field($_GET['report_id']);
			$postID = (int) sanitize_text_field($_GET['post']);
			$filename = 'post-' . $postID . '-block-' . sanitize_title($reportID) . '-' . date('d-m-Y__H-i-s');

			header("Pragma: public");
			header("Expires: 0");
			header("Cache-Control: private", false);
			header("Content-type: text/csv");
			header("Content-Disposition: attachment; filename={$filename}.csv");

			$fp = fopen('php://output', 'w');

			// Get results
			global $wpdb;
			$results = $wpdb->get_results("SELECT * FROM `" . BLOCK_OPTIN_TABLE_NAME . "` WHERE `post_id` = {$postID} AND `report_id` = '{$reportID}'");

			// ------------------------------------------------------------------------------------------------------------------------------
			addPostDetailsToReport($fp, $postID, $reportID);

			fputcsv($fp, ['id', 'post_id', 'report_id', 'email', 'data', 'time']);

			// Output data
			foreach ($results as $result) {
				fputcsv(
					$fp,
					[
						$result->id,
						$result->post_id,
						$result->report_id,
						$result->email,
						str_replace('"', '', json_encode(unserialize($result->data))),
						$result->time,
					]
				);
			}

			fclose($fp);
			exit;
		}
	}
}
