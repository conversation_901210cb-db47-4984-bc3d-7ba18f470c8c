/* global tinymce */
(function () {
    tinymce.PluginManager.add('dropdown_shortcode', function (editor, url) {
        editor.addButton('dropdown_shortcode', {
            text: 'Dropdown',
            icon: false,
            onclick: function () {
                editor.windowManager.open({
                    title: 'Add Dropdown',
                    body: [
                        {
                            type: 'container',
                            html: '<p>Creates a dropdown/accordion element</p>'
                        },
                        {
                            type: 'textbox',
                            name: 'title',
                            label: 'Title',
                            value: '',
                            minWidth: 300,
                            placeholder: 'Enter the title/question text'
                        },
                        {
                            type: 'textbox',
                            name: 'description',
                            label: 'Description',
                            value: '',
                            multiline: true,
                            minHeight: 150,
                            minWidth: 400,
                            placeholder: 'Enter the content that appears when expanded'
                        },
                        {
                            type: 'checkbox',
                            name: 'outlined',
                            label: 'Outlined style',
                            checked: false
                        },
                        {
                            type: 'checkbox',
                            name: 'fullwidth',
                            label: 'Full width',
                            checked: false
                        }
                    ],
                    onsubmit: function (e) {
                        // Validate required fields
                        if (!e.data.title.trim()) {
                            alert('Title is required');
                            return false;
                        }

                        // Get description and convert line breaks to <br/> tags
                        var description = e.data.description || 'Enter your description here';
                        description = description.replace(/\n/g, '<br/>');

                        // Build shortcode attributes
                        var shortcodeAttrs = 'title="' + e.data.title + '"';

                        // Add outlined attribute if checkbox is checked
                        if (e.data.outlined) {
                            shortcodeAttrs += ' outlined="true"';
                        }

                        // Add fullwidth attribute if checkbox is checked
                        if (e.data.fullwidth) {
                            shortcodeAttrs += ' fullwidth="true"';
                        }

                        // Insert shortcode with line breaks around content
                        editor.insertContent('[dropdown ' + shortcodeAttrs + ']<br/>' + description + '<br/>[/dropdown]');
                    }
                });
            },
        });
    });
})();
