<div class="background content-stretchable">
	<div class="survey__middle">
		<form id="js-survey-form">
			<div class="survey__container">
				<!-- Survey Headers -->
				<div class="survey__header survey__header--visible survey__header--progress js-survey-header-progress">
					<div class="survey__title"><?= $title; ?></div>
					<div class="survey__progress">
						<div class="survey__progress__count js-survey-progress-count">-/-</div>
						<div class="survey__progress__bar">
							<div class="survey__progress__bar__done js-survey-progress-bar"></div>
						</div>
					</div>
				</div>

				<div class="survey__header survey__header--review js-survey-header-review">
					<div class="survey__title"><?= $translations['review_skipped_questions']; ?></div>
					<div class="survey__progress">
						<div class="survey__progress__count js-survey-progress-review"></div>
					</div>
				</div>

				<div class="survey__header survey__header--visible survey__action--skip survey__action--skip--mobile js-survey-skip-mobile">
					<a href="#" class="survey__skip js-survey-skip"><?= $translations['skip'] ?></a>
				</div>

				<!-- Survey Contents -->
				<div class="survey__content survey__content--steps js-survey-content-steps survey__content--visible">
					<?php foreach ($questions as $stepNr => $question): ?>
						<?php
						$stepClass = getClass([
							'survey__step js-survey-step',
							[
								'condition' => $stepNr === 0,
								'name' => 'survey__step--active',
							]
						]);
						?>
						<div class="<?= $stepClass; ?>" data-step="<?= ++$stepNr; ?>" data-step-skip="" data-question-type=<?= $question['type']; ?>>
							<input type="hidden" name="survey_questions[<?= $stepNr; ?>][skip]" value="" class="js-survey-step_skip">
							<div class="survey__question"><?= $question['title']; ?></div>
							<?php if (!empty($question['subtitle'])): ?>
								<div class="survey__hint survey__hint--step"><?= $question['subtitle']; ?></div>
							<?php endif; ?>
							<div class="survey__input">
								<?php switch ($question['type']) {
									case 'feedback-slider':
										get_template_part('components/form/feedback-slider', null, [
											'name'    => 'survey_questions[' . $stepNr . '][answer]',
											'lower_label' => $question['range_lower'] ?? '',
											'higher_label'   => $question['range_higher'] ?? '',
										]);
										break;

									case 'multi-select-checkbox':
										$options = [];
										foreach ($question['multiselect_options'] as $option) {
											$options[] = [
												'value' => $option['option'] ?? '',
												'label' => $option['option'] ?? '',
											];
										}
										get_template_part('components/form/multi-select-checkbox', null, [
											'name'    => 'survey_questions[' . $stepNr . '][answer]',
											'class'   => 'multi-select-checkbox--center',
											'options' => $options,
										]);
										break;

									case 'yes-no-radio':
										$options = [
											'yes' => [
												'value' => $question['yes_option'] ?? '',
												'label' => $question['yes_option'] ?? '',
											],
											'no' => [
												'value' => $question['no_option'] ?? '',
												'label' => $question['no_option'] ?? '',
											],
										];
										$hints = [
											'unselected' => $translations['tap_to_select'] ?? '',
											'selected'   => $translations['selected'] ?? '',
										];
										get_template_part('components/form/yes-no-radio', null, [
											'name'    => 'survey_questions[' . $stepNr . '][answer]',
											'options' => $options,
											'hints'   => $hints,
										]);
										break;

									case 'textarea':
										get_template_part('components/form/textarea', null, [
											'name'  => 'survey_questions[' . $stepNr . '][answer]',
											'label' => $question['text_input'],
											'value' => '',
											'dynamic-height' => true,
										]);
										break;
								} ?>
							</div>
						</div>
					<?php endforeach; ?>

					<div class="survey__error js-survey-error-message">
						<i class="survey__error__icon"><?= vector('info'); ?></i>
						<?= $translations['please_answer_label']; ?>
					</div>
				</div>

				<div class="survey__content survey__content--almost-done js-survey-content-almost-done">
					<div class="survey__question"><?= $translations['almost_done_title']; ?></div>
					<div class="survey__hint survey__hint--hidden js-survey-submit-hint-almost-done"><?= $translations['almost_done_subtitle_submit']; ?></div>
					<div class="survey__hint survey__hint--hidden js-survey-review-hint-almost-done" data-text="<?= $translations['almost_done_subtitle_review']; ?>"></div>
					<i class="survey__question-icon survey__question-icon--hidden js-survey-loader-icon"><?= vector('loader'); ?></i>

					<div class="survey__error js-survey-submit-error-message">
						<i class="survey__error__icon"><?= vector('info'); ?></i>
						<?= $translations['submit_error_label']; ?>
					</div>
				</div>

				<div class="survey__content survey__content--done js-survey-content-done">
					<i class="survey__question-icon"><?= vector('check'); ?></i>
					<div class="survey__question"><?= $translations['done_title']; ?></div>
					<div class="survey__hint"><?= $translations['done_subtitle']; ?></div>
				</div>

				<!-- Survey Buttons -->
				<div class="survey__buttons survey__buttons--progress js-survey-buttons-progress survey__buttons--visible">
					<div class="survey__action survey__action--skip survey__action--skip--dektop">
						<a href="#" class="survey__skip js-survey-skip"><?= $translations['skip'] ?></a>
					</div>
					<button class="survey__action survey__action--prev btn btn--small btn--outlined btn--with-icon js-survey-previous survey__action--hidden" type="button">
						<i class="btn__icon"><?= vector('arrow-left'); ?></i>
						<?= $translations['previous'] ?>
					</button>
					<button class="survey__action survey__action--next btn btn--small js-survey-next" type="button"><?= $translations['next'] ?></button>
				</div>

				<div class="survey__buttons survey__buttons--almost-done js-survey-buttons-almost-done">
					<button class="survey__action survey__action--review-answers btn btn--small-from-md btn--outlined btn--with-icon js-survey-review-answers survey__action--hidden" type="button">
						<i class="btn__icon"><?= vector('arrow-left'); ?></i>
						<?= $translations['review_answers'] ?>
					</button>
					<button class="survey__action survey__action--submit btn btn--small-from-md js-survey-submit" type="button"><?= $translations['submit'] ?></button>
				</div>

				<div class="survey__buttons survey__buttons--done js-survey-buttons-done">
					<a href="<?= $ctaUrl; ?>" class="survey__action survey__action--start-playing btn btn--small-from-md btn--primary" type="button"><?= $translations['start_playing'] ?></a>
				</div>
			</div>
		</form>
	</div>
</div>