<?php
// Register desktop menu and hamburger mobile menu
register_nav_menus([
    'menu-1' => esc_html__('Primary', 'phoenix'),
    'mobile-menu' => esc_html__('Mobile Menu', 'phoenix'),
]);

// Register menu for new tabbed global nav links
if(!empty(AUTHORITIES[CURRENT_BRAND])) {
    foreach(array_keys(AUTHORITIES[CURRENT_BRAND]) as $lang) {
        // Register mobile menu type
        register_nav_menus([
            'mobile-menu_' . $lang=> esc_html__('Mobile Menu - ' . $lang, 'phoenix'),
            // 'desktop-menu_' . $lang => esc_html__('Desktop Menu - ' . $lang, 'phoenix'), // In case we wanna sync desktop menu in the future
        ]);
    }
}

// Get menus in a structured way to make render(view) code have better readability
function wp_get_menu_array($current_menu)
{
	$array_menu = wp_get_nav_menu_items($current_menu);
	$menu = array();
	foreach ($array_menu as $m) {
		if (empty($m->menu_item_parent)) {
			$menu[$m->ID] = array();
			$menu[$m->ID]['ID']          = $m->ID;
			$menu[$m->ID]['title']       = $m->title;
			$menu[$m->ID]['description'] = $m->description;
			$menu[$m->ID]['url']         = $m->url;
			$menu[$m->ID]['classes']     = $m->classes;
			$menu[$m->ID]['children']    = array();
		}
	}
	$subMenu = array();
	foreach ($array_menu as $m) {
		if ($m->menu_item_parent) {
			$subMenu[$m->ID] = array();
			$subMenu[$m->ID]['ID']          = $m->ID;
			$subMenu[$m->ID]['title']       = $m->title;
			$subMenu[$m->ID]['description'] = $m->description;
			$subMenu[$m->ID]['url']         = $m->url;
			$subMenu[$m->ID]['classes']     = $m->classes;
			$menu[$m->menu_item_parent]['children'][$m->ID] = $subMenu[$m->ID];
		}
	}
	return $menu;
}

class Phnx_Walker_Nav_Menu extends Walker_Nav_Menu
{

	function start_lvl(&$output, $depth = 0, $args = array())
	{
		$indent = str_repeat("\t", $depth);
		$output .= "\n$indent<ul class=\"navigation__menu__submenu\">\n";
	}

	function start_el(&$output, $item, $depth = 0, $args = array(), $id = 0)
	{

		global $wp_query;
		$indent = ($depth) ? str_repeat("\t", $depth) : '';

		$class_names = $value = '';

		$classes = empty($item->classes) ? array() : (array) $item->classes;
		$classes[] = 'menu-item-' . $item->ID;

		$class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));

		// Check our custom has_children property.here is the points
		if (in_array('menu-item-has-children', $classes) && $depth == 0) {
			// Your Code
			$class_names = ' class="dropdown custom-drop ' . esc_attr($class_names) . '"';
		} else {
			$class_names = ' class="' . esc_attr($class_names) . '"';
		}

		$id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args);
		$id = strlen($id) ? ' id="' . esc_attr($id) . '"' : '';

		$output .= $indent . '<li' . $id . $value . $class_names . '>';

		$attributes  = !empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) . '"' : '';
		$attributes .= !empty($item->target)     ? ' target="' . esc_attr($item->target) . '"' : '';
		$attributes .= !empty($item->xfn)        ? ' rel="'    . esc_attr($item->xfn) . '"' : '';
		$attributes .= !empty($item->url)        ? ' href="'   . esc_attr($item->url) . '"' : '';

		// Add GTM tracking value as data attribute if available
		if (!empty($item->gtm_tracking)) {
			$attributes .= ' data-gtm-tracking="' . esc_attr($item->gtm_tracking) . '"';
		}

		$item_output = $args->before;

		$item_output .= '<a' . $attributes . '>';
		$item_output .= $args->link_before . apply_filters('the_title', $item->title, $item->ID) . $args->link_after;
		// Check our custom has_children property.here is the points
		if (in_array('menu-item-has-children', $classes) && $depth == 0) {
			// Dropdown chevron (bottom.svg)

			if(!empty( $args->theme_location) && $args->theme_location !== 'mobile-menu') {
				$item_output .= vector('dropdown-down');
			}
		}
		$item_output .= '</a>';
		$item_output .= $args->after;

		$output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
	}
}