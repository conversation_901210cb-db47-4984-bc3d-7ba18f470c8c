<?php
function addPostDetailsToReport($fp, $postID, $reportID = null) {
    $post = get_post($postID);
    fputcsv($fp, ['Page Title:', $post->post_title]);
    fputcsv($fp, ['Page URL:', get_permalink($postID)]);
    fputcsv($fp, ['Page published by:', $post->post_author ? get_the_author_meta('display_name', $post->post_author) : '']);
    fputcsv($fp, ['Page published on the date:', date('Y-m-d H:i:s', strtotime($post->post_date))]);
    $expiry_date = get_field('expiry_date', $postID);
    if (!empty($expiry_date)) {
        fputcsv($fp, ['Page published until:', date('Y-m-d H:i:s', strtotime(get_field('expiry_date', $postID)))]);
    }
    if(!empty($reportID)) {
        fputcsv($fp, ['Report ID:', $reportID]);
    }
    fputcsv($fp, ['']);
}