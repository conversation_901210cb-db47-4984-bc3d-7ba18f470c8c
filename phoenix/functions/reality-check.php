<?php
/**
 * Reality Check AJAX Endpoint
 * Proxies reality check notifications to avoid CORS issues
 */

// AJAX handler for Reality Check
add_action('wp_ajax_reality_check', 'handle_reality_check');
add_action('wp_ajax_nopriv_reality_check', 'handle_reality_check');

function handle_reality_check() {
    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'reality_check_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $action = $_POST['rc_action'] ?? '';

    if ($action === 'get_next_notification') {
        // Fetch reality check data from the real endpoint
        $url = brandUrl() . '/reality-check/notifications/next';

        // Prepare headers with sessionId cookie if available
        $headers = [];
        if (isset($_COOKIE['sessionId'])) {
            $headers['Cookie'] = 'sessionId=' . $_COOKIE['sessionId'];
        }

        $response = wp_remote_get($url, [
            'timeout' => 5,
            'headers' => $headers
        ]);

        if (is_wp_error($response)) {
            wp_send_json_error('Failed to fetch reality check data');
            return;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (json_last_error() === JSON_ERROR_NONE) {
            wp_send_json_success($data);
        } else {
            wp_send_json_error('Invalid JSON response');
        }
    } else {
        wp_send_json_error('Invalid action');
    }
}

// Add nonce for AJAX requests
add_action('wp_enqueue_scripts', 'add_reality_check_nonce');
function add_reality_check_nonce() {
    wp_localize_script('main', 'reality_check', [
        'nonce' => wp_create_nonce('reality_check_nonce')
    ]);
}