<?php
// Add compliance to body class by filter.
function addComplianceClassToBodyClass($classes)
{
    global $HAS_COMPLIANCE;

    if(in_array(CURRENT_REGION, COMPLIANCE_HEADER_REGIONS_FOR_LOGGED_OUT)) {
        $HAS_COMPLIANCE = (empty($_GET['body']) && get_field_tweaked('compliance_header', 'option'));
    }

    if (in_array(CURRENT_REGION, COMPLIANCE_HEADER_REGIONS_FOR_LOGGED_IN)) {
        if(player()->isLoggedIn()) {
            $HAS_COMPLIANCE = (empty($_GET['body']) && get_field_tweaked('compliance_header', 'option'));
        } else {
            if(!isset($HAS_COMPLIANCE)) {
                $HAS_COMPLIANCE = false;
            }
        }
    }

    if ($HAS_COMPLIANCE) {
        $complianceHeader = 'has-compliance'; // Main compliance header class

        $classes[] = $complianceHeader;
        if (CURRENT_REGION == 'de') {
            $classes[] = $complianceHeader . '--gga'; // Germany
        }
        if (CURRENT_REGION == 'sv') {
            $classes[] = $complianceHeader . '--sga'; // Sweden
        }
        if (CURRENT_REGION == 'nl') {
            $classes[] = $complianceHeader . '--ksa'; // Netherlands
        }
    }
    return $classes;
}
add_filter('add_body_class', 'addComplianceClassToBodyClass');

// Add classes from campaign settings acf to body class by filter.
function addCampaignSettingsClassToBodyClass($classes)
{
    global $isStarPageHeaderLayout;
    $isStarPageHeaderLayout = get_field('header_layout') == 'start-page';

    if (get_field('hide_header')) {
        $classes[] = 'has-no-header';
    } else {
        if (get_field('transparent_header') || $isStarPageHeaderLayout) {
            $classes[] = 'has-transparent-header';

            if (get_field('transparent_header_scrolled') || $isStarPageHeaderLayout) {
                $classes[] = 'has-transparent-header--scrolled';
            }
        }

        if (get_field('hide_header_logo') || $isStarPageHeaderLayout) {
            $classes[] = 'has-no-logo';
        }

        if (get_field('hide_header_menu') || $isStarPageHeaderLayout) {
            $classes[] = 'has-no-menu';
        }
    }

    if (get_field('full_screen_content')) {
        $classes[] = 'has-full-screen-content';
    }

    if (!empty(get_field('jump_links'))) {
        $classes[] = 'has-jump-links';
    }

    if (get_field('animation_blocks')) {
        $classes[] = 'has-animation-blocks';
    }

    if (get_field('full_screen_blocks')) {
        $classes[] = 'has-full-screen-blocks';

        if (get_field('snap_scroll_blocks')) {
            $classes[] = 'has-snap-scroll-blocks';
        }
    }

    return $classes;
}
add_filter('add_body_class', 'addCampaignSettingsClassToBodyClass');

// Add has-tabs to body class by filter.
function addTabsClassToBodyClass($classes)
{
    if (get_field('include_tabs') && have_rows('tabs_links')) {
        $classes[] = 'has-tabs';
    }
    return $classes;
}
add_filter('add_body_class', 'addTabsClassToBodyClass');

// Add content-only to body class by filter.
function addContentOnlyClassToBodyClass($classes)
{
    if (isCurrentDeviceAnApp()) {
        $classes[] = 'content-only--footer';
    }

    if (!empty($_GET['body'])) {
        if($_GET['body'] == '1' || $_GET['body'] == 'true') {
            $classes[] = 'content-only--footer';
        } elseif($_GET['body'] == '11') {
            $classes[] = 'content-only';
        }
    }
    return $classes;
}
add_filter('add_body_class', 'addContentOnlyClassToBodyClass');
