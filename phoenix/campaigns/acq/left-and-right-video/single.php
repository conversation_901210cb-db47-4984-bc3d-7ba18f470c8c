<?php addBackgroundInlineStyle('.background', $backgroundGroup); ?>

<div class="background content-stretchable">

    <?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
    <?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>

    <div class="headline-section mobile-header-title">
        <?php ob_start(); ?>
        <?php get_template_part('campaigns/parts/image-above-title'); ?>
        <?php get_template_part('campaigns/parts/title'); ?>
        <?php $headingElement = ob_get_clean(); ?>
        <?= $headingElement; ?>
    </div>
    <div class="headline-section normal-header">
        <?php
            $contentContainerClass = getClass([
                'content-margin campaign__content container wrapper wrapper--md-80',
                [
                    'condition' => !empty($layout_settings['full_width']),
                    'name' => 'wrapper--wider'
                ],
                [
				    'condition' => (!empty(get_field('include_stepper')) && !empty(get_field('vertical_steps'))),
                    'name' => 'content-margin-top',
                ]
            ]);
        ?>
        <div class="<?= $contentContainerClass; ?>">
            <?php
                $gridLeftAndRightContainerClass = getClass([
                    'grid-left-and-right',
                    [
                        'condition' => !empty($layout_settings['columns_size']),
                        'name' => 'grid-left-and-right--' . $layout_settings['columns_size'],
                    ]
                ]);
            ?>
            <div class="<?= $gridLeftAndRightContainerClass; ?>">
                <div class="grid-left-and-right__left">
                    <div class="title-section">
                        <?= $headingElement; ?>
                    </div>
                    <?php
                    if (isset($game['game_name']) && trim((string) $game['game_name']) != "") : ?>
                        <a class="lr-game" href="<?= (trim((string) $game['game_url']) == '') ? '#' : $game['game_url'] ?>">
                            <div>
                                <img src="<?= $game['game_image'] ?>" alt="<?= $game['game_name'] ?>">
                            </div>
                            <div>
                                <h3><?= $game['game_name'] ?></h3>
                                <h4><?= $game['subtitle'] ?></h4>
                            </div>
                        </a>
                    <?php endif; ?>

                    <?php get_template_part('campaigns/parts/subtitle'); ?>
                    <?php get_template_part('campaigns/parts/description'); ?>

                    <?php get_template_part('campaigns/parts/jackpots'); ?>

                    <?php get_template_part('components/terms-and-conditions', null, ['place' => 'above_cta']); ?>

                    <?php ob_start(); ?>
                    <?php
                        $buttonClass = getClass([
                            'btn',
                            [
                                'condition' => !empty($content['button_color']),
                                'name' => 'btn--' . $content['button_color'],
                            ],
                            [
                                'condition' => !empty($content['button_size']),
                                'name' => 'btn--' . $content['button_size'],
                            ],
                            [
                                'condition' => !empty($content['button_animation']),
                                'name' => 'btn--animation-' . $content['button_animation'],
                            ],
                            [
                                'condition' => !empty($content['button_tracking']),
                                'name' => 'js-' . $content['button_tracking'],
                                'else-name' => 'js-button-claim',
                            ]
                        ]);
                    ?>
                    <a href="<?= $content['button_url']; ?>"
                    class="<?= $buttonClass; ?>"><?= $content['button_text']; ?></a>

                    <?php
					$stickyButtonElement = ob_get_clean();
					get_template_part('campaigns/parts/cta-button-sticky', null, [
						'button' => $stickyButtonElement,
						'sticky' => empty($content['not_sticky_mobile_button']),
					]);
					?>

                    <?php get_template_part('components/payment-logos', null, ['show' => $content['payment_logos_toggle']]); ?>

                    <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_cta']); ?>

                </div>
                <div class="grid-left-and-right__right">
                    <?php
                    if(!empty($image)) {
                        $imageArgs = array_merge($image,
                            [
                                'alt' => $content['campaign_header'],
                                'link' => !empty($content['button_url']) ? $content['button_url'] : ''
                            ]
                        );
                        get_template_part('campaigns/parts/offer-media', null, $imageArgs);
                    }
                    ?>
                </div>
            </div>

            <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_content']); ?>
        </div>
    </div>
</div>

<?php get_template_part('components/steps', null, ['cta_link' => !empty($content['button_url']) ? $content['button_url'] : '', 'type' =>  'top-icon']); ?>

<div class="container wrapper">
    <?php get_template_part('components/recent-winners'); ?>
</div>