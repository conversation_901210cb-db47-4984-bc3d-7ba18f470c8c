<?php addBackgroundInlineStyle('.background', $backgroundGroup); ?>
<?php if(!empty($backgroundOld)) : ?>
	<style>.campaign .background{background-image:url(<?= $backgroundOld; ?>)}</style>
<?php endif; ?>

<div class="background content-stretchable">

    <?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
    <?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>

    <?php
        $contentContainerClass = getClass([
            'content-margin campaign__content container wrapper',
            [
                'condition' => !empty($content['invert_text_color']),
                'name' => 'campaign__content--invert-font-color',
                'else-name' => 'campaign__content--normal-font-color',
            ],
			[
				'condition' => (!empty(get_field('include_stepper')) && !empty(get_field('vertical_steps'))),
				'name' => 'content-margin-top',
			]
        ]);
    ?>
    <div class="<?= $contentContainerClass; ?>">
        <?php
        if(!empty($image)) {
            $imageArgs = array_merge($image,
                [
                    'alt' => $content['campaign_header'],
                    'link' => $content['button_url']
                ]
            );
            get_template_part('campaigns/parts/offer-media', null, $imageArgs);
        }
        ?>

        <?php get_template_part('campaigns/parts/image-above-title'); ?>
        <?php get_template_part('campaigns/parts/title'); ?>
        <?php get_template_part('campaigns/parts/subtitle'); ?>
        <?php get_template_part('campaigns/parts/description'); ?>

        <?php get_template_part('campaigns/parts/jackpots'); ?>

        <?php get_template_part('components/terms-and-conditions', null, ['place' => 'above_cta']); ?>

        <?php ob_start(); ?>
        <?php
        $buttonClass = getClass([
            'btn',
            [
                'condition' => !empty($content['button_color']),
                'name' => 'btn--' . $content['button_color'],
            ],
            [
                'condition' => !empty($content['button_size']),
                'name' => 'btn--' . $content['button_size'],
            ],
            [
                'condition' => !empty($content['button_animation']),
                'name' => 'btn--animation-' . $content['button_animation'],
            ],
            [
                'condition' => !empty($content['button_tracking']),
                'name' => 'js-' . $content['button_tracking'],
                'else-name' => 'js-button-claim',
            ]
        ]);
        ?>
        <a href="<?= $content['button_url']; ?>" class="<?= $buttonClass; ?>"><?= $content['button_text']; ?></a>
        <?php
        $stickyButtonElement = ob_get_clean();
        get_template_part('campaigns/parts/cta-button-sticky', null, [
            'button' => $stickyButtonElement,
            'sticky' => empty($content['not_sticky_mobile_button']),
        ]);
        ?>

        <?php get_template_part('components/payment-logos', null, ['show' => $content['payment_logos_toggle']]); ?>


        <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_cta']); ?>
        <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_content']); ?>
    </div>

    <?php if (get_field('include_stepper')) : ?>
        <div class="info-page__steps">
            <?php get_template_part('components/steps', null, ['cta_link' => !empty($content['button_url']) ? $content['button_url'] : '']); ?>
        </div>
    <?php endif; ?>
</div>

<?php if(have_rows('block_with_icon')): ?>
    <div class="info-page__blocks">
        <div class="container wrapper">
            <?php if (get_field('section_header')) : ?>
                <h3><?= get_field('section_header'); ?></h3>
            <?php endif; ?>
            <ul class="block-grid">
                <?php while (have_rows('block_with_icon')) : the_row(); ?>
                    <li>
                        <img src="<?= get_sub_field('block_icon'); ?>" alt="<?= get_sub_field('block_header'); ?>">
                        <h4><?= get_sub_field('block_header'); ?></h4>
                        <p><?= get_sub_field('block_description'); ?></p>
                    </li>
                <?php endwhile; ?>
            </ul>
        </div>
    </div>
<?php endif; ?>

<?php if($big_image['image_type'] != 'none'): ?>
    <div class="info-page__image">
        <div class="container wrapper">
            <h3><?= get_field('image_title'); ?></h3>
            <p><?= get_field('image_description'); ?></p>
            <?php
            if(!empty($image)) {
                $imageArgs = array_merge($big_image,
                    [
                        'alt' => get_field('image_title')
                    ]
                );
                get_template_part('campaigns/parts/offer-media', null, $imageArgs);
            }
            ?>
        </div>
    </div>
<?php endif; ?>