<?php addBackgroundInlineStyle('.background', $backgroundGroup); ?>
<?php addBackgroundInlineStyle('.question .question__image', $questionBackgroundGroup); ?>

<div class="background content-stretchable">

	<?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
	<?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>

	<div class="campaign__content recommendation-process recommendation-process--start active" data-content="start-step">

        <?php
            $contentContainerClass = getClass([
                'content-margin campaign__content container wrapper wrapper--md-80',
				[
					'condition' => !empty($content['invert_text_color']),
					'name' => 'campaign__content--invert-font-color',
					'else-name' => 'campaign__content--normal-font-color',
				],
                [
                    'condition' => !empty($layout_settings['full_width']),
                    'name' => 'wrapper--wider'
                ],
				[
					'condition' => (!empty(get_field('include_stepper')) && !empty(get_field('vertical_steps'))),
					'name' => 'content-margin-top',
				]
            ]);
        ?>
        <div class="<?= $contentContainerClass; ?>">
            <?php
                $gridLeftAndRightContainerClass = getClass([
                    'grid-left-and-right',
                    [
						'condition' => ( isset($layout_settings['columns_size']) && !empty($layout_settings['columns_size']) ),
						'name' => 'grid-left-and-right--' . ( $layout_settings['columns_size'] ?? '' ),
                    ]
                ]);
            ?>
			<div class="<?= $gridLeftAndRightContainerClass; ?>">
				<!-- content -->
				<div class="grid-left-and-right__left">

					<?php get_template_part('campaigns/parts/image-above-title'); ?>
					<?php get_template_part('campaigns/parts/title'); ?>
					<?php get_template_part('campaigns/parts/subtitle'); ?>
					<?php get_template_part('campaigns/parts/description'); ?>

					<?php get_template_part('components/terms-and-conditions', null, ['place' => 'above_cta']); ?>

					<?php ob_start(); ?>
					<?php
					$buttonClass = getClass([
						'btn',
						[
							'condition' => ( isset($content['button_color']) && !empty($content['button_color']) ),
							'name' => 'btn--' . ( $content['button_color'] ?? '' ),
						],
						[
							'condition' => ( isset($content['button_size']) && !empty($content['button_size']) ),
							'name' => 'btn--' . ( $content['button_size'] ?? '' ),
						],
						[
							'condition' => !empty($content['button_animation']),
							'name' => 'btn--animation-' . $content['button_animation'],
						],
                        [
                            'condition' => !empty($content['button_tracking']),
                            'name' => 'js-' . $content['button_tracking'],
                            'else-name' => 'js-button-claim',
                        ],
						'js-start_personal_slot_recommendation'
					]);
					?>
					<button class="<?= $buttonClass ?>"><?= $content['button_text']; ?></button>
					<?php
					$stickyButtonElement = ob_get_clean();
					get_template_part('campaigns/parts/cta-button-sticky', null, [
						'button' => $stickyButtonElement,
						'sticky' => empty($content['not_sticky_mobile_button']),
						'content' => 'start-step',
						'visible' => true
					]);
					?>

					<?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_cta']); ?>

				</div>
				<!-- image -->
				<div class="grid-left-and-right__right">
				<?php
				if(!empty($image)) {
                    $imageArgs = array_merge($image,
						[
							'alt' => $content['campaign_header']
						]
					);
                    get_template_part('campaigns/parts/offer-media', null, $imageArgs);
				}
				?>
				</div>
			</div>
			<?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_content']); ?>
		</div>
		<?php get_template_part('components/steps'); ?>
	</div>

	<?php
        $contentContainerClass = getClass([
            'campaign__content recommendation-process recommendation-process--questions',
            [
                'condition' => !empty($content['invert_text_color']),
                'name' => 'campaign__content--invert-font-color',
                'else-name' => 'campaign__content--normal-font-color',
            ]
        ]);
    ?>
	<div class="<?= $contentContainerClass; ?>">
		<div class="question__container content-margin container wrapper wrapper--md-80">
			<div class="question__placeholder">
				<?php
				// need to revers the order of the questions because with position: absolute; the last question is on top and then the question are iterated from last to first
				$questionNr = count($slotQuestions);
				while ($questionNr) :
					$questionNr = $questionNr - 1;
					$question = $slotQuestions[$questionNr];
					$activeClass = '';
					if ($questionNr == 0) {
						$activeClass = 'active';
					}
					?>
					<div class="question <?= $activeClass; ?>" data-question-nr="<?= $questionNr; ?>">
						<div class="question__image">
							<img src="<?= $question['question_image']; ?>" alt="<?= $question['question_header'] ?>">
						</div>
						<div class="question__content">
							<div class="question__header">
								<?= $question['question_header'] ?>
							</div>
							<div class="question__description">
								<?= $question['question_description'] ?>
							</div>
						</div>

						<div class="question__choice m--reject"><?= vector('close-circle'); ?></div>
						<div class="question__choice m--like"><?= vector('heart'); ?></div>
					</div>
				<?php endwhile; ?>
			</div>

			<div class="question__answers">
				<button class="question__btn question__btn--reject js-answer_personal_slot_recommendation" data-question-answer="0"><?= vector('close-circle'); ?></button>
				<button class="question__btn question__btn--like js-answer_personal_slot_recommendation" data-question-answer="1"><?= vector('heart'); ?></button>
			</div>
			<a href="javascript:void();" class="question__skip js-skip_personal_slot_recommendation"><?= $skipButtonText; ?></a>
		</div>
	</div>

	<?php
        $contentContainerClass = getClass([
            'campaign__content recommendation-process recommendation-process--match',
            [
                'condition' => !empty($content['invert_text_color']),
                'name' => 'campaign__content--invert-font-color',
                'else-name' => 'campaign__content--normal-font-color',
            ]
        ]);
    ?>
	<div class="<?= $contentContainerClass; ?>">
		<div class="match content-margin container wrapper wrapper--tight">
			<h1 class="match__heading"><?= $matchHeader ?></h1>
			<div class="match__description">
				<?= $matchDescription; ?>
			</div>
			<div id="personal_slot_recommendation_games" class="slot-games"></div>
			<div id="personal_slot_recommendation_games_loading" class="d-none text-center">
                <?= vector('loader'); ?>
            </div>
			<a href="javascript:void();" class="question__skip js-restart_personal_slot_recommendation"><?= $tryAgainButtonText; ?></a>
		</div>
	</div>
</div>
