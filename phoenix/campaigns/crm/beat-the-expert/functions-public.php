<?php
global $bte_table_name;

// Get questions from ACF
function getPredictions($post_id = false) {
    $predictions = null;

    if (have_rows('predictions', $post_id)) {
        $i = 0;  // loop through the rows of data

        while (have_rows('predictions', $post_id)) {
            the_row();

            switch (get_sub_field('type')) {
                case 'multiple':
                    $predictions[$i] = ['multiple' => get_sub_field('multiple_choice')];
                    break;

                case 'matchup':
                    $predictions[$i] = ['matchup' => get_sub_field('matchup')];
                    break;

                case 'both_teams_score':
                    $predictions[$i] = ['both_teams_score' => get_sub_field('both_teams_score')];
                    break;

                case 'free':
                    $predictions[$i] = ['free' => get_sub_field('free_form_question')];
                    break;

                case 'yesno':
                    $predictions[$i] = ['yesno' => get_sub_field('yes_no_question')];
                    break;
            }

            // include type
            $predictions[$i]['type'] = get_sub_field('type');

            $i++; // increment counter
        }
    }

    return $predictions;
}

// Get correct answers from ACF
//
// $post_id - optional parameter
function getCorrectAnswers($post_id = false) {
    $correctAnswers = [];

    $acfData = get_field('predictions', $post_id);

    foreach ($acfData as $data) {
        switch ($data['type']) {
            case 'multiple':
                if (!empty($data['multiple_choice']['correct_prediction'])) {
                    array_push($correctAnswers, $data['multiple_choice']['correct_prediction']);
                }
                break;

            case 'matchup':
                if (!empty($data['matchup']['result'])) {
                    array_push($correctAnswers, getOutcome($data['matchup']['result']));
                }
                break;

            case 'both_teams_score':
                if (!empty($data['both_teams_score']['result'])) {
                    array_push($correctAnswers, didBothTeamScore($data['both_teams_score']['result']));
                }
                break;

            case 'free':
                if (!empty($data['free_form_question']['correct_predictions'])) {
                    array_push($correctAnswers, $data['free_form_question']['correct_predictions']);
                }
                break;

            case 'yesno':
                if (!empty($data['yes_no_question']['correct_prediction'])) {
                    array_push($correctAnswers, $data['yes_no_question']['correct_prediction']);
                }
                break;
        }
    }
    return $correctAnswers;
}

// Validate predictions
function validatePredictions($predictions, $sentUserPredictions, $enabledDoubleChance = true) {
    $questionWith2ChosenOption = 0;

    foreach ($predictions as $i => $prediction) {
        switch ($prediction['type']) {
            case 'multiple':
                if (!in_array($sentUserPredictions[$i], ['a', 'b', 'c', 'd'])) {
                    return false;
                }
                break;

            case 'matchup':
                $count = count($sentUserPredictions[$i]);
                if ($count != 1) {
                    if ($enabledDoubleChance && $count == 2 && $questionWith2ChosenOption < 3) {
                        $questionWith2ChosenOption++;
                    } else {
                        return false;
                    }
                }
                foreach ($sentUserPredictions[$i] as $sentUserPrediction) {
                    if (!in_array($sentUserPrediction, ['1', 'X', '2'])) {
                        return false;
                    }
                }
                break;

            case 'both_teams_score':
                if (!in_array($sentUserPredictions[$i], ['Yes', 'No'])) {
                    return false;
                }
                break;

            case 'free':
                if (empty(trim((string) $sentUserPredictions[$i]))) {
                    return false;
                }
                break;

            case 'yesno':
                if (!in_array($sentUserPredictions[$i], ['Yes', 'No'])) {
                    return false;
                }
                break;
        }
    }

    if (count($predictions) != count($sentUserPredictions)) {
        return false;
    }

    return true;
}

function isDuplicatePrediction($email, $predictions) {
    global $wpdb, $bte_table_name;
    $post_id = get_the_ID();
    if (is_array($predictions)) {
        $predictions = serialize($predictions);
    } else {
        // Handle cases where $predictions might not be an array
        $predictions = serialize([$predictions]);
    }
    $duplicates = $wpdb->get_results("SELECT email FROM $bte_table_name WHERE post_id = {$post_id} AND email = '{$email}'  AND predictions = '{$predictions}'");
    return !empty($duplicates) && true;
}

// Submit the predictions for the first time
function submitUserPrediction($userPredictions, $odds) {

    // Check duplicates to bail early
    if(isDuplicatePrediction(player()->getEmail(), $userPredictions)) return true;

    $package = [
        'post_id' => get_the_ID(),
        'username' => player()->getUsername(),
        'email' =>  player()->getEmail(),
        'region' => CURRENT_PATH,
        'time' => getNow()->format('Y-m-d H:i:s'),
        'predictions' => serialize($userPredictions),
        'odds' => ($odds) ? serialize($odds) : 0
    ];

    global $wpdb, $bte_table_name;
    return $wpdb->insert($bte_table_name, $package);
}

// Update user predictions
function updateUserPrediction($userPredictions, $odds) {
    $update = [
        'time' => getNow()->format('Y-m-d H:i:s'),
        'predictions' => serialize($userPredictions),
        'odds' => ($odds) ? serialize($odds) : 0
    ];

    $where = [
        'post_id' => get_the_ID(),
        'username' => player()->getUsername()
    ];

    global $wpdb, $bte_table_name;
    return $wpdb->update($bte_table_name, $update, $where);
}

// Get the submitted predictions by the user
function getUserSentPredictions() {
    $predictions = [];

    if (!empty($_POST['predictions']) && is_array($_POST['predictions'])) {
        foreach ($_POST['predictions'] as $prediction) {
            // append prediction to array
            if (is_array($prediction)) {
                foreach ($prediction as $key => $option) {
                    $prediction[$key] = sanitize_text_field($option);
                }
            } else {
                $prediction = sanitize_text_field($prediction);
            }
            array_push($predictions, $prediction);
        }
    }
    return $predictions;
}

// Check if the user has made any predictions
function getUserPredictionsFromDb() {
    global $wpdb, $bte_table_name;

    if (!player()->isLoggedIn()) {
        return false;
    }

    $post_id = get_the_ID();
    $result = $wpdb->get_results("SELECT predictions FROM $bte_table_name WHERE post_id = {$post_id} AND username = '" . player()->getUsername() . "'");

    if (empty($result)) {
        return false;
    } else {
        return unserialize($result[0]->predictions);
    }
}

// Check whether the user is correct
function isUserCorrect($prediction, $correct) {
    if (is_array($correct)) {
        /* Its a freeform question! */
        foreach ($correct as $variant) {
            if (strcasecmp((string) $prediction, (string) $variant['correct_prediction']) == 0) {
                return true;
            }
        }
    } elseif (is_array($prediction)) {
        /* Its a matchup */
        if (in_array($correct, $prediction)) {
            return true;
        }
    } else {
        /* non matchup question */
        if (strcasecmp((string) $prediction, (string) $correct) == 0) {
            return true;
        }
    }

    return false;
}

// Get odds from matches based on predictions
function getOddsArray($predictions, $userPredictions) {
    $odds = [];

    foreach ($predictions as $i => $prediction) {
        if ($prediction['type'] == 'matchup' && !empty($userPredictions) && !empty($userPredictions[$i])) {
            $actualUserPredictions = $userPredictions[$i];
            $isDoubleChance = (count($actualUserPredictions) == 2);

            // Double chance odds in array
            if ($isDoubleChance) {
                $doublePredictions = [];

                foreach ($actualUserPredictions as $doubleChancePrediction) {
                    array_push($doublePredictions, getOdds($prediction['matchup']['select_a_sport'], $prediction['matchup']['league_id'], $prediction['matchup']['match_id'], $doubleChancePrediction));
                }

                array_push($odds, $doublePredictions);
            } else {
                $actualUserPredictions = current($actualUserPredictions);
                $matchOdds[$actualUserPredictions] = getOdds($prediction['matchup']['select_a_sport'], $prediction['matchup']['league_id'], $prediction['matchup']['match_id'], $actualUserPredictions);

                // Fallback if no odds available
                if ($matchOdds[$actualUserPredictions] == 0) {
                    $matchOdds[$actualUserPredictions] = 1;
                }

                array_push($odds, $matchOdds[$actualUserPredictions]);
            }
        } else {
            array_push($odds, 1); // If it's not a matchup
        }
    }
    do_action('qm/debug', 'Odds Array: ' . json_encode($odds));

    return $odds;
}

// Display expert tips or not
function displayExpertPrediction($settings, $nowIsBiggerThanCutoff) {
    return ($settings['expert_tips'] && ($nowIsBiggerThanCutoff || $settings['expert_tips_before_cutoff']));
}

// Get match outcome by format
function getOutcome($result) {
    $result = explode('-', (string) $result);
    return ($result[0] > $result[1]) ? "1" : (($result[0] == $result[1]) ? "X" : "2");
}

// Did Both team score?
function didBothTeamScore($result) {
    $result = explode('-', (string) $result);
    if ($result[0] > 0 && $result[1] > 0) {
        return 'Yes';
    }
    return 'No';
}

// Calculate user score
function getScore($userPredictions, $correctAnswers, $odds) {
    $score = 0;

    if (empty($userPredictions)) {
        return $score;
    }

    foreach ($userPredictions as $i => $prediction) {
        if (isUserCorrect($prediction, $correctAnswers[$i])) {
            if ($odds) {
                // Double chance
                if (is_array($odds[$i]) && count($odds[$i]) == 2) {
                    $multiple = 1;
                    $sum = 0;

                    foreach ($odds[$i] as $fixtureOdds) {
                        $multiple *= floatval($fixtureOdds);
                        $sum += floatval($fixtureOdds);
                    }

                    $score += ($multiple / $sum);
                } else {
                    $score += floatval($odds[$i]);
                }
            } else {
                $score++;
            }
            do_action('qm/debug', 'Correct Answer #' . ($i + 1) . '; Score: ' . $score);
        }
    }

    return round($score, 2);
}

function generateBTEReport($postID) {
    // $filename - name of file
    $filename = sanitize_title(get_the_title($postID)) . '-' . date('d-m-Y__H-i-s');

    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control: private", false);
    header("Content-type: text/csv");
    header("Content-Disposition: attachment; filename={$filename}.csv");

    $fp = fopen('php://output', 'w');

    // Get predictions
    $predictions = getPredictions($postID);

    // Get correct answers
    $correctAnswers = getCorrectAnswers($postID);

    // Get results
    global $wpdb, $bte_table_name;
    $results = $wpdb->get_results("SELECT * FROM `$bte_table_name` WHERE `post_id` = {$postID}");

    // Display scores sorted
    if (count($correctAnswers) == count($predictions)) {
        foreach ($results as $result) {
            if ($result->odds) {
                $result->score = getScore(unserialize($result->predictions), $correctAnswers, unserialize($result->odds));
            }
        }

        usort($results, function ($a, $b) {
            return strcmp((string) $b->score, (string) $a->score);
        });
    }

    // ------------------------------------------------------------------------------------------------------------------------------
    addPostDetailsToReport($fp, $postID);

    fputcsv($fp, array_merge(['id', 'post_id', 'username', 'email'], (defined('ICL_LANGUAGE_CODE') ? ['region'] : []), ['time', 'predictions', 'score']));

    // Output data
    foreach ($results as $result) {
        $predictions = unserialize($result->predictions);
        foreach ($predictions as $key => $prediction) {
            if (is_array($prediction)) {
                $predictions[$key] = implode('||', $prediction);
            }
        }

        fputcsv(
            $fp,
            array_merge(
                [
                    $result->id,
                    $result->post_id,
                    $result->username,
                    $result->email,
                ],
                (defined('ICL_LANGUAGE_CODE') ? [$result->region] : []),
                [
                    $result->time,
                    implode(',', $predictions),
                    $result->score
                ]
            )
        );
    }

    fclose($fp);
    exit;
}
