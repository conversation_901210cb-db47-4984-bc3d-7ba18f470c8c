<?php
function submitUserPredictionBET($predictions)
{
    global $wpdb;

    $package = [
        'post_id' => get_the_ID(),
        'username' => player()->getUsername(),
        'region' => CURRENT_PATH,
        'time' => getNow()->format('Y-m-d H:i:s'),
        'predictions' => $predictions
    ];

    $table_name = 'comeon_beat_the_experts';
    return $wpdb->insert($table_name, $package);
}

function updateUserPredictionBET($predictions)
{
    global $wpdb;

    $table_name = 'comeon_beat_the_experts';
    $where = [
        'post_id' => get_the_ID(),
        'username' => player()->getUsername(),
    ];
    $update = [
        'time' => getNow()->format('Y-m-d H:i:s'),
        'predictions' => $predictions
    ];

    return $wpdb->update($table_name, $update, $where);
}

/*
 * Output: {user, predictions, in this format}
 * */
function getUserSubmittedPrediction($form)
{
    if (isset($_GET['predictions'])) {
        // return predictions submitted by user
        $i = 0;
        // Only 3 question with double answered allowed
        $len = count($_GET['predictions']);
        $prediction = '{';
        if (count($_GET['predictions']) != count($form)) { // Player didn't answer at least one of questions
            return false;
        }

        foreach ($_GET['predictions'] as $question) {
            // append item to object
            if (is_array($question)) { // Only happen in checkboxes type
                $question = implode("||", $question);
            }
            $prediction = $prediction . sanitize_text_field($question);

            // add comma if not last item
            if ($i != $len - 1)
                $prediction = $prediction . ',';

            $i++;
        }
        return $prediction . '}';
    } else {
        return false;
    }
}

function displayExpertPredictionBET($settings, $nowIsBiggerThanCutoff)
{
    return ($settings['expert_tips'] && ($nowIsBiggerThanCutoff || $settings['expert_tips_before_cutoff']));
}

function hasUserPredicted()
{
    // Output: Array of predictions / False.
    global $wpdb;

    if (!player()->isLoggedIn()) {
        return false;
    }

    $post_id = get_the_ID();
    $result = $wpdb->get_results("SELECT predictions FROM comeon_beat_the_experts WHERE post_id = {$post_id} AND username = '" . player()->getUsername() . "'");

    if (empty($result)) {
        return false;
    } else {
        return explode(',', substr((string) $result[0]->predictions, 1, -1));
    }
}

function getForm()
{
    // Output: Prediction questions from ACF
    $predictions = null;
    if (have_rows('predictions')) :
        $i = 0;  // loop through the rows of data

        while (have_rows('predictions')) : the_row();

            switch (get_sub_field('type')):

                case 'multiple':
                    $predictions[$i] = ['multiple' => get_sub_field('multiple_choice')];
                    break;

                case 'matchup':
                    $predictions[$i] = ['matchup' => get_sub_field('matchup')];
                    break;

                case 'both_teams_score':
                    $predictions[$i] = ['both_teams_score' => get_sub_field('both_teams_score')];
                    break;

                case 'free':
                    $predictions[$i] = ['free' => get_sub_field('free_form_question')];
                    break;

                case 'yesno':
                    $predictions[$i] = ['yesno' => get_sub_field('yes_no_question')];
                    break;

            endswitch;

            // include type
            $predictions[$i]['type'] = get_sub_field('type');

            $i++; // increment counter
        endwhile;
    endif;
    return $predictions;
}

function getCorrectPrediction($post_id = false)
{

    $obj = (object) [
        'correctPrediction' => [],
        'displayScore' => false,
        'questionCount' => 0
    ];

    $acfData = get_field('predictions', $post_id);
    $i = 0;
    $check = 0;

    foreach ($acfData as $data) :
        switch ($data['type']):

            case 'multiple':
                if ($data['multiple_choice']['correct_prediction']) :
                    $obj->correctPrediction[$i] = $data['multiple_choice']['correct_prediction'];
                    $check++;
                endif;
                break;

            case 'matchup':
                if (!empty($data['matchup']['result'])) :
                    $obj->correctPrediction[$i] = getOutcomeBET($data['matchup']['result']);
                    $check++;
                endif;
                break;

            case 'both_teams_score':
                if (!empty($data['both_teams_score']['result'])) :
                    $obj->correctPrediction[$i] = didBothTeamScoreBET($data['both_teams_score']['result']);
                    $check++;
                endif;
                break;

            case 'free':
                if ($data['free_form_question']['correct_predictions']) :
                    $obj->correctPrediction[$i] = $data['free_form_question']['correct_predictions'];
                    $check++;
                endif;
                break;

            case 'yesno':
                if ($data['yes_no_question']['correct_prediction']) :
                    $obj->correctPrediction[$i] = $data['yes_no_question']['correct_prediction'];
                    $check++;
                endif;
                break;

        endswitch;
        $i++;
    endforeach;

    $obj->questionCount = $i;

    if ($i == $check)
        $obj->displayScore = true;

    return $obj;
}

function getOutcomeBET($result)
{
    $result = explode('-', (string) $result);
    return ($result[0] > $result[1]) ? "1" : (($result[0] == $result[1]) ? "X" : "2");
}

// Did Both team score?
function didBothTeamScoreBET($result) {
    $result = explode('-', (string) $result);
    if ($result[0] > 0 && $result[1] > 0) {
        return 'Yes';
    }
    return 'No';
}

function isUserCorrectBET($prediction, $correct)
{
    $pattern = '/([|])/';
    preg_match($pattern, (string) $prediction, $matches);
    if (is_array($correct)) {
        /* Its a freeform question! */
        foreach ($correct as $variant) :
            if (strcasecmp((string) $prediction, (string) $variant['correct_prediction']) == 0) {
                return true;
            }
        endforeach;
    } elseif ($matches) {
        /* Its a matchup */
        $prediction = explode('|', substr((string) $prediction, 1, -1));
        if (strcasecmp($prediction[1], (string) $correct) == 0) {
            return true;
        }
    } else {
        /* non matchup question */
        if (strcasecmp((string) $prediction, (string) $correct) == 0) {
            return true;
        }
    }

    return false;
}

function getScoreBET($score, $predictions)
{

    $pattern = '/([|])/';
    $i = 0;
    $result = 0;

    foreach ($predictions as $prediction) :
        preg_match($pattern, (string) $prediction, $matches);

        if (is_array($score->correctPrediction[$i])) {
            foreach ($score->correctPrediction[$i] as $variant) {
                if (strcasecmp((string) $prediction, (string) $variant['correct_prediction']) == 0) {
                    $result++;
                }
            }
        } elseif ($matches) {
            $prediction = explode('|', substr((string) $prediction, 1, -1));
            if (strcasecmp($prediction[1], (string) $score->correctPrediction[$i]) == 0) {
                $result++;
            }
        } else {
            if (strcasecmp((string) $prediction, (string) $score->correctPrediction[$i]) == 0) {
                $result++;
            }
        }
        $i++;
    endforeach;

    return $result;
}

function generateLeftAndRightBTEReport($postID) {
    // $filename - name of file
    $filename = sanitize_title(get_the_title($postID)) . '-' . date('d-m-Y__H-i-s');

    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control: private", false);
    header("Content-type: text/csv");
    header("Content-Disposition: attachment; filename={$filename}.csv");

    $fp = fopen('php://output', 'w');

    // get correct prediction
    $score = getCorrectPrediction($postID);

    // Get results
    global $wpdb;
    $table_name = 'comeon_beat_the_experts';
    $results = $wpdb->get_results("SELECT * FROM `$table_name` WHERE `post_id` = {$postID}");

    /* If scores are gonna be displayed, set it up and sort it first */
    if ($score->displayScore) :
        foreach ($results as $result) :
            $result->score = getScoreBET($score, explode(',', substr((string) $result->predictions, 1, -1)));
        endforeach;
        usort($results, function ($a, $b) {
            return strcmp((string) $b->score, (string) $a->score);
        });
    endif;

    // ------------------------------------------------------------------------------------------------------------------------------
    addPostDetailsToReport($fp, $postID);

    fputcsv($fp, ['id', 'post_id', 'username', 'region', 'time', 'predictions', 'score']);

    // Output data
    foreach ($results as $result) {
        if (!$result->score)
            $result->score = "¯\_(ツ)_/¯";

        fputcsv($fp,[
            $result->id,
            $result->post_id,
            $result->username,
            $result->region,
            $result->time,
            $result->predictions,
            $result->score
        ]);
    }

    fclose($fp);
    exit;
}
