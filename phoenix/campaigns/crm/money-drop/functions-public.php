<?php
// get user answers
function getMoneyDropAnswers($postID = null) {
    $result = [];

    if (player()->isLoggedIn()) {
        global $wpdb;

        $postID = $postID ?? get_the_ID();
        $table_name = MONEY_DROP_DB;
        $result = $wpdb->get_results("SELECT * FROM `$table_name` WHERE `post_id` = {$postID} AND `pid` = '" . player()->getId() . "'", ARRAY_A);
    }

    return $result;
}

// Submit the user answer for the first time
function submitMoneyDropAnswer($postID, $questionID, $percentage, $optionA, $optionB) {
    $package = [
        'post_id'         => $postID,
        'pid'             => player()->getId(),
        'question_id'     => $questionID,
        'amount_option_a' => $optionA,
        'amount_option_b' => $optionB,
        'percentage'      => $percentage,
        'datetime'        => getNow()->format('Y-m-d H:i:s'),
    ];

    global $wpdb;
    $table_name = MONEY_DROP_DB;
    return $wpdb->insert($table_name, $package);
}

// Update user answer
function updateMoneyDropAnswer($postID, $questionID, $percentage, $optionA, $optionB) {
    $update = [
        'amount_option_a' => $optionA,
        'amount_option_b' => $optionB,
        'percentage'      => $percentage,
        'datetime'        => getNow()->format('Y-m-d H:i:s'),
    ];

    $where = [
        'post_id'     => $postID,
        'pid'         => player()->getId(),
        'question_id' => $questionID,
    ];

    global $wpdb;
    $table_name = MONEY_DROP_DB;
    return $wpdb->update($table_name, $update, $where);
}

function getMoneyDropQuestionState($startAmount, $questions, $answers) {
    $totalAmount = intval($startAmount);

    if (empty($questions) || !is_array($questions))
        return ['id' => 0, 'status' => MONEY_DROP_QUESTION_STATUS['game-over'], 'total' => 0];

    foreach ($questions as $questionID => $question) {
        $now = getNow();

        if ($now < $question['countdown_start'])
            return ['id' => $questionID, 'status' => MONEY_DROP_QUESTION_STATUS['next-soon'], 'total' => $totalAmount];

        if ($now < $question['countdown_to'])
            return ['id' => $questionID, 'status' => MONEY_DROP_QUESTION_STATUS['question'], 'total' => $totalAmount];

        if (!in_array($question['result'], ['option_a', 'option_b']))
            return ['id' => $questionID, 'status' => MONEY_DROP_QUESTION_STATUS['result-soon'], 'total' => $totalAmount];

        $answer = getMoneyDropAnswerToQuestion($answers, $questionID);

        if (empty($answer) || empty($answer['amount_' . $question['result']]))
            return  ['id' => $questionID, 'status' => MONEY_DROP_QUESTION_STATUS['game-over'], 'total' => 0];

        $totalAmount = intval($answer['amount_' . $question['result']]);
    }

    return ['id' => $questionID, 'status' => MONEY_DROP_QUESTION_STATUS['game-over'], 'total' => $totalAmount];
}

function getMoneyDropAnswerToQuestion($answers, $questionID) {
    $filtered = array_filter($answers, function ($answer) use ($questionID) {
        if ($answer['question_id'] == $questionID) {
            return $answer;
        }
    });
    $result = array_pop($filtered);
    return $result;
}

function generateMoneyDropReport($postID) {
    // $filename - name of file
    $filename = sanitize_title(get_the_title($postID)) . '-' . date('d-m-Y__H-i-s');

    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control: private", false);
    header("Content-type: text/csv");
    header("Content-Disposition: attachment; filename={$filename}.csv");

    $fp = fopen('php://output', 'w');

    // Get Questions
    $questions = get_field('questions', $postID);

    // Get Answers
    global $wpdb;
    $table_name = MONEY_DROP_DB;
    $answers = $wpdb->get_results("SELECT * FROM `$table_name` WHERE `post_id` = {$postID}", ARRAY_A);

    // ------------------------------------------------------------------------------------------------------------------------------
    addPostDetailsToReport($fp, $postID);

    fputcsv($fp, ['id', 'Question', 'Values', 'Result', 'PID', 'date of selection', 'amt on Option A', 'amt on Option B']);

    // Output data
    foreach ($answers as $answer) {
        $question = $questions[$answer['question_id']];
        fputcsv($fp, [
            $answer['id'],
            $question['title'],
            $question['option_a'] . ' - ' . $question['option_b'],
            $question[$question['result']],
            decryptedPID($answer['pid']),
            $answer['datetime'],
            $answer['amount_option_a'],
            $answer['amount_option_b']
        ]);
    }

    fclose($fp);
    exit;
}
