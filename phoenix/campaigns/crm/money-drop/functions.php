<?php
define('MON<PERSON><PERSON>_DROP_DB', 'px_money_drop_answers');
define('MONEY_DROP_QUESTION_STATUS', [
    'next-soon' => 'next question soon',
    'question' => 'question active for submit',
    'result-soon' => 'result ready soon',
    'game-over' => 'game is over',
]);

require_once(locate_template('campaigns/crm/money-drop/functions-public.php'));

// Create Money Drop Answers table in database
add_action('after_switch_theme', 'money_drop_activate_database');
function money_drop_activate_database() {
    global $wpdb;
    $table_name = MONEY_DROP_DB;

    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
        `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
        `post_id` BIGINT(20) UNSIGNED NOT NULL,
        `pid` VARCHAR(50) NOT NULL,
        `question_id` INT(10) UNSIGNED NOT NULL,
        `amount_option_a` INT(10) UNSIGNED NOT NULL,
        `amount_option_b` INT(10) UNSIGNED NOT NULL,
        `percentage` INT(10) UNSIGNED NOT NULL,
        `datetime` datetime DEFAULT current_timestamp() NOT NULL
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    $wpdb->query($sql);
    if ($wpdb->last_error !== '')
        do_action('qm/error', 'Error creating table ' . $table_name . ': ' . $wpdb->last_error . ' Query: ' . $wpdb->last_query . ' Result: ' . $wpdb->last_result);
}

// Download Report button
add_action('post_submitbox_misc_actions', 'money_drop_report_button', 10, 1);
function money_drop_report_button($post) {
    global $wpdb;
    $screen = get_current_screen();
    $template = get_the_primary_term($post->ID, CAMPAIGN_TEMPLATE_SLUG);
    $templateSlug = null;
    if (!empty($template)) {
        $templateSlug = $template->slug;
    }

    if ($screen->post_type !== CAMPAIGN_SLUG || $templateSlug !== 'money-drop')
        return;

    $table_name = MONEY_DROP_DB;
    $query = "SELECT COUNT(*) FROM {$table_name} WHERE post_id = {$post->ID}";

    $count = $wpdb->get_var($query);

    ?>
    <div id="major-publishing-actions" style="overflow:hidden">
        <div id="publishing-action" style="float: unset; text-align: left;">
            <span style="line-height: 30px;">Entries: <strong><?= $count ?></strong></span>
            <a style="float: right;" target="_blank" download href="<?= add_query_arg(['download'=>'report'], get_edit_post_link($post->ID)) ?>" class="button-primary acf-button">Download Report</a>
        </div>
    </div>
    <?php
}

add_action('admin_init', 'getMoneyDropRepot', 1, 0);
function getMoneyDropRepot() {
    global $pagenow;
    if (is_admin() && $pagenow == 'post.php' && isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['post']) && isset($_GET['download']) && $_GET['download'] == 'report') {
        $template = get_the_primary_term($_GET['post'], CAMPAIGN_TEMPLATE_SLUG);
        $templateSlug = !empty($template) ? $template->slug : null;
        if ($templateSlug == 'money-drop')
            generateMoneyDropReport($_GET['post']);
    }
}

// AJAX handler to money drop answer sumbit
add_action('wp_ajax_submit_money_drop_anwser', 'submit_money_drop_anwser');
add_action('wp_ajax_nopriv_submit_money_drop_anwser', 'submit_money_drop_anwser');
function submit_money_drop_anwser() {
    $percentage = intval($_POST['percentage']);
    if (!player()->isLoggedIn() || empty($_POST['post_id']) || $percentage < 0 || $percentage > 100) {
        wp_send_json_error();
    }

    $answers = getMoneyDropAnswers($_POST['post_id']);
    foreach ($answers as $key => $answer) {
        $answers[$key]['datetime'] = getDateTime($answer['datetime']);
    }

    $startAmount = get_field('starting_amount', $_POST['post_id']);

    $questions = get_field('questions', $_POST['post_id']);
    foreach ($questions as $questionID => $question) {
        $questions[$questionID]['countdown_start'] = getDateTime($question['countdown_start']);
        $questions[$questionID]['countdown_to'] = getDateTime($question['countdown_to']);
    }

    $questionsState = getMoneyDropQuestionState($startAmount, $questions, $answers);

    if ($questionsState['status'] != MONEY_DROP_QUESTION_STATUS['question']) {
        wp_send_json_error();
    }

    $currentAnswer = getMoneyDropAnswerToQuestion($answers, $questionsState['id']);

    $total = $questionsState['total'];
    $optionB = round($total * $percentage / 100);
    $optionA = $total - $optionB;

    if (empty($currentAnswer)) {
        submitMoneyDropAnswer($_POST['post_id'], $questionsState['id'], $percentage, $optionA, $optionB);
    } else {
        updateMoneyDropAnswer($_POST['post_id'], $questionsState['id'], $percentage, $optionA, $optionB);
    }

    wp_send_json_success();
}
