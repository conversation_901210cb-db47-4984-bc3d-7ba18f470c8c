<?php addBackgroundInlineStyle('.campaign .background', $backgroundGroup); ?>

<div class="money-drop__step money-drop__step--visible js-money-drop-welcome-content" data-content="start-step">
	<div class="background content-stretchable">

		<?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
		<?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>

		<?php
			$contentContainerClass = getClass([
				'container content-margin campaign__content campaign__content--centered wrapper wrapper--tight',
				[
					'condition' => !empty($content['invert_text_color']),
					'name' => 'campaign__content--invert-font-color',
					'else-name' => 'campaign__content--normal-font-color',
				],
				[
					'condition' => (!empty(get_field('include_stepper')) && !empty(get_field('vertical_steps'))),
					'name' => 'content-margin-top',
				]
			]);
		?>
		<div class="<?= $contentContainerClass; ?>">
			<?php
			if(!empty($image)) {
				$imageArgs = array_merge($image,
					[
						'type' => 'small',
						'alt' => $content['campaign_header']
					]
				);
				get_template_part('campaigns/parts/offer-media', null, $imageArgs);
			}
			?>

			<?php get_template_part('campaigns/parts/title'); ?>
			<?php get_template_part('campaigns/parts/subtitle'); ?>
			<?php get_template_part('campaigns/parts/description'); ?>


			<?php get_template_part('components/terms-and-conditions', null, ['place' => 'above_cta']); ?>

			<?php ob_start(); ?>
			<?php
			$buttonClass = getClass([
				'btn',
				[
					'condition' => !empty($content['button_color']),
					'name' => 'btn--' . $content['button_color'],
				],
				[
					'condition' => !empty($content['button_size']),
					'name' => 'btn--' . $content['button_size'],
				],
				[
					'condition' => !empty($content['button_animation']),
					'name' => 'btn--animation-' . $content['button_animation'],
				],
				[
					'condition' => !empty($content['button_tracking']) && player()->isLoggedIn(),
					'name' => 'js-' . $content['button_tracking'],
				]
			]);
			?>
			<?php if (player()->isLoggedIn()) : ?>
				<a href="#show-questions" class="<?= $buttonClass ?>"><?= $content['button_text']; ?></a>
			<?php else : ?>
				<a href="#show-questions" onClick="login()" class="<?= $buttonClass ?> js-button-login"><?= $content['button_text']; ?></a>
			<?php endif; ?>
			<?php
			$stickyButtonElement = ob_get_clean();
			get_template_part('campaigns/parts/cta-button-sticky', null, [
				'button'  => $stickyButtonElement,
				'sticky'  => empty($content['not_sticky_mobile_button']),
				'content' => 'start-step',
				'visible' => true
			]);
			?>

			<?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_cta']); ?>
			<?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_content']); ?>
		</div>

		<?php get_template_part('components/steps'); ?>
	</div>
</div>

<?php include(locate_template('campaigns/crm/money-drop/questions.php', false, false)); ?>