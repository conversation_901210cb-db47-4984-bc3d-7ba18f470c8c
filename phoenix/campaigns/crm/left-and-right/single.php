<?php
addBackgroundInlineStyle('.background', $backgroundGroup); ?>

<div class="background content-stretchable">

    <?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
    <?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>

    <?php
        $contentContainerClass = getClass([
            'content-margin campaign__content container wrapper wrapper--md-80',
            [
                'condition' => !empty($content['invert_text_color']),
                'name' => 'campaign__content--invert-font-color',
                'else-name' => 'campaign__content--normal-font-color',
            ],
            [
                'condition' => !empty($layout_settings['full_width']),
                'name' => 'wrapper--wider'
            ],
			[
				'condition' => (!empty(get_field('include_stepper')) && !empty(get_field('vertical_steps'))),
				'name' => 'content-margin-top',
			]
        ]);
    ?>
	<div class="<?= $contentContainerClass; ?>">
		<?php
            $gridLeftAndRightContainerClass = getClass([
                'grid-left-and-right',
                [
                    'condition' => !empty($layout_settings['columns_size']),
                    'name' => 'grid-left-and-right--' . ($layout_settings['columns_size'] ?? ''),
                ]
            ]);
		?>
		<div class="<?= $gridLeftAndRightContainerClass; ?>">
            <!-- content -->
            <div class="grid-left-and-right__left">
                <?php get_template_part('campaigns/parts/image-above-title'); ?>
				<?php get_template_part('campaigns/parts/title'); ?>
				<?php get_template_part('campaigns/parts/subtitle'); ?>
				<?php get_template_part('campaigns/parts/description'); ?>

                <?php get_template_part('campaigns/parts/jackpots'); ?>

                <?php get_template_part('components/terms-and-conditions', null, ['place' => 'above_cta']); ?>

                <?php
                if ($isCountdownEnabled) {
                    get_template_part('campaigns/parts/countdown-sticky', null, [
                        'status' => $status,
                        'time_end' => $countdownEnd,
					    'time_start' => $countdownStart,
                        '24h' => $countdown24h,
                    ]);
                }
                ?>

                <?php
                $buttonClass = getClass([
                    'btn btn--full-to-md',
                    [
                        'condition' => !empty($content['button_color']),
                        'name' => 'btn--' . $content['button_color'],
                    ],
                    [
                        'condition' => !empty($content['button_size']),
                        'name' => 'btn--' . $content['button_size'],
                    ],
                    [
                        'condition' => !empty($content['button_animation']),
                        'name' => 'btn--animation-' . $content['button_animation'],
                    ],
                    [
                        'condition' => !empty($content['button_tracking']),
                        'name' => 'js-' . $content['button_tracking'],
                        'else-name' => 'js-button-claim',
                    ]
                ]);
                ?>

                <?php ob_start(); ?>
                <?php if ($isOptinTemplate) : ?>
                    <?php get_template_part('components/optin'); ?>
                    <div class="optin_claim_button js-optin-offer-cta">
                        <a href="<?= $content['button_url']; ?>"
                        class="<?= $buttonClass; ?>"><?= $content['button_text']; ?></a>
                    </div>
                <?php else : ?>
                    <?php if ($isLoginButtonEnabled && !player()->isLoggedIn()) : ?>
                        <a href="#" onClick="login();" class="btn js-button-login"><?= $loginButtonText ?></a>
                    <?php elseif (!$pauseCampaign) : ?>
                        <a href="<?= $content['button_url']; ?>"
                        class="<?= $buttonClass; ?>"><?= $content['button_text']; ?></a>
                    <?php endif; ?>
                <?php endif; ?>
                <?php
                $stickyButtonElement = ob_get_clean();
                get_template_part('campaigns/parts/cta-button-sticky', null, [
                    'button' => $stickyButtonElement,
                    'sticky' => empty($content['not_sticky_mobile_button']),
                ]);
                ?>

                <?php get_template_part('components/payment-logos', null, ['show' => $content['payment_logos_toggle']]); ?>

                <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_cta']); ?>

            </div>
            <!-- video or image -->
            <div class="grid-left-and-right__right">
                <?php
                if(!empty($image)) {
                    $imageArgs = array_merge($image,
                        [
                            'alt' => $content['campaign_header'],
                            'link' => $content['button_url']
                        ]
                    );
                    get_template_part('campaigns/parts/offer-media', null, $imageArgs);
                }
				?>
            </div>
        </div>

        <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_content']); ?>
    </div>

    <?php get_template_part('components/steps', null, ['cta_link' => !empty($content['button_url']) ? $content['button_url'] : '']); ?>
</div>

<?php if ($hasWinners) : ?>
    <div class="container wrapper wrapper--md-80">
        <?php get_template_part('components/recent-winners'); ?>
    </div>
<?php endif; ?>
