<?php
// Offer of the Day - Controller
// -----------------------------------------------------------------

global $wp;
global $post;
global $content;

$backgroundGroup = get_field('background_group');

$content = get_field('content');

// Offers
$offers = get_field('offers');
$currentOfferGroup = false;
$currentOfferGroupKey = false;
$currentOfferSteps = [];

$numberFakeOffers = get_field('number_fake_offers');
$numberDisplayOffers = get_field('number_display_offers');
$blurredImageUrl = get_field('blurred_image');
$blurredImageTitle = get_field('blurred_image_title');

$status = COUNTDOWN_STARTS_IN; // Not yet started
$eligible = true;

$today = getDateTime('today');
$start = getDateTime(get_field('countdown_start'));

// Countdown related variables
$countdownStart = $today;
$countdownEnd = $start;

if ($today >= $start) {
    $countdownStart = $start;

    foreach ($offers as $offerKey => $offer) {
        $countdownEnd = getDateTime($offer['offer_duration']);

        if ($today <= $countdownEnd && player()->checkEligibility($offer['rtpg_settings']['rtpg_notification_group'])) {
            // Overwrite initial content once time conditions are met
            $status = COUNTDOWN_NEW_OFFER_IN; // Has started

            if ($today == $countdownEnd) {
                // Current active offer in slider
                $status = COUNTDOWN_OFFER_ENDS_IN;
                if ($offerKey + 1 == count($offers) && $numberFakeOffers == 0) {
                    // Last countdown offer.
                    $status = COUNTDOWN_CAMPAIGN_ENDS_IN;
                }

                $countdownStart = $today;
                $countdownEnd = getDateTime('tomorrow');

                $currentOfferGroupKey = $offerKey;
                $currentOfferGroup = $offer;
            }
            break;
        }
        $status = COUNTDOWN_EXPIRED; // All countdown offers done / expired
    }
}

$loginButtonText = get_field_tweaked('login', 'option');

$sliderOffers = [];
foreach ($offers as $offerKey => $offer) {
    $rtpgSettings = $offer['rtpg_settings'];
    $eligible = player()->checkEligibility($rtpgSettings['rtpg_notification_group']);

    if ($eligible) {
        $sliderOffers[$offerKey] = $offer['regular_offer'];

        if ($offer['enable_segmented_offer']) {
            $sliderOffers[$offerKey] = getContentForSegmentValue($offer);
        }

        $end = getDateTime($offer['offer_duration']);

        $sliderOffers[$offerKey]['offer_duration'] = formatDateRelativeMedium($end);
        if ($today > $end) {
            $sliderOffers[$offerKey]['offer_duration'] = get_field_tweaked('expired', 'option');
            if (empty($sliderOffers[$offerKey]['offer_duration'])) {
                // Get default value of acf field as fallback
                $sliderOffers[$offerKey]['offer_duration'] = acf_get_field('expired')["default_value"];
            }
        }
    } else {
        $lastFoundNonEligibleRTPGSettings = $rtpgSettings;
        $eligible = false;
    }
}

if (isset($lastFoundNonEligibleRTPGSettings)) {
    $rtpgSettings = $lastFoundNonEligibleRTPGSettings;
}

$numberDisplayOffers += array_search($currentOfferGroupKey, array_keys($sliderOffers));

if (player()->isLoggedIn() && $currentOfferGroup) {
    $currentOffer = $currentOfferGroup['regular_offer'];

    if ($currentOfferGroup['enable_segmented_offer']) {
        $currentOffer = getContentForSegmentValue($currentOfferGroup);
    }

    $content = $currentOffer['content'];

    $steps = [];
    for ($ctr = 1; $ctr <= 3; $ctr++) {
        $steps[$ctr] = $currentOffer['step-' . $ctr];
    }

    $currentOfferSteps = [
        'include_stepper' => $currentOffer['include_stepper'],
        'cta_link' => $content['button_url'] ?? '',
        'steps' => $steps,
        'has_icons' => $currentOffer['use_step_icons'],
        'background_color' => $currentOffer['custom_background_color'],
        'text_color' =>  $currentOffer['custom_text_color'],
        'has_link' => $currentOffer['use_cta_link'],
        'vertical' =>  $currentOffer['vertical_steps'],
    ];
}
