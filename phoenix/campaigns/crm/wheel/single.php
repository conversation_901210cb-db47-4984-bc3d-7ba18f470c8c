<?php
addBackgroundInlineStyle('.background', $backgroundGroup); ?>
<div class="background content-stretchable">

    <?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>
    <?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
    <?php
        $contentContainerClass = getClass([
            'content-margin campaign__content container wrapper wrapper--md-80',
            [
                'condition' => !empty($content['invert_text_color']),
                'name' => 'campaign__content--invert-font-color',
                'else-name' => 'campaign__content--normal-font-color',
            ],
			[
				'condition' => (!empty(get_field('include_stepper')) && !empty(get_field('vertical_steps'))),
				'name' => 'content-margin-top',
			]
        ]);
    ?>
    <div class="<?= $contentContainerClass; ?>">
        <div class="grid-left-and-right">
            <!-- content -->
            <div class="grid-left-and-right__left">
                <?php get_template_part('campaigns/parts/image-above-title'); ?>
				<?php get_template_part('campaigns/parts/title'); ?>
				<?php get_template_part('campaigns/parts/subtitle'); ?>

                <div class="campaign__description">
                    <?php if (player()->isLoggedIn() && !empty($currentOffer)) : // if logged in then show before spin description and after spin change it to the offer description ?>
                        <div class="before-spin-description">
                            <?= $beforeSpinDescription; ?>
                        </div>
                        <div class="offer-description">
                            <?php get_template_part('components/read-more', null, $content['campaign_description']); ?>
                        </div>
                    <?php else : // not logged in description ?>
                        <?php get_template_part('components/read-more', null, $content['campaign_description']); ?>
                    <?php endif; ?>
                </div>

                <?php get_template_part('campaigns/parts/jackpots', null, [
                    'show'     => ( !empty($content['show_jackpots']) ? $content['show_jackpots'] : '' ),
                    'provider' => ( !empty($content['clone_jackpot_provider']['jackpot_provider']) ? $content['clone_jackpot_provider']['jackpot_provider'] : '' ),
                ]); ?>

                <?php get_template_part('components/terms-and-conditions', null, ['place' => 'above_cta']); ?>

                <?php
                if ( isset($isCountdownEnabled) && $isCountdownEnabled ) {
                    get_template_part('campaigns/parts/countdown-sticky', null, [
                        'status' => $status,
                        'time_end' => $countdownEnd,
                        'time_start' => $countdownStart,
                    ]);
                }
                ?>

                <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_cta']); ?>
            </div>
            <!-- image -->
            <div class="grid-left-and-right__right">
                <div class="wheel-game">
                    <img class="wheel-logo" src="<?= $wheel['logo']; ?>" alt="wheel-logo">
                    <div class="offer-holder">
                        <img class="wheel-arrow" src="<?= $wheel['arrow']; ?>" alt="wheel-arrow">
                        <img class="wheel-circle" src="<?= $wheel['wheel']; ?>" alt="wheel-circle">
                        <?php if (player()->isLoggedIn()) : ?>
                            <a class="wheel-offer" href="<?= $content['button_url']; ?>" title="<?= $content['button_text']; ?>">
                                <img src="<?= $image; ?>" alt="<?= $content['button_text'] ?: 'offer-image'; ?>">
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <?php ob_start(); ?>
                <?php
                $buttonClass = getClass([
                    'btn',
                    [
                        'condition' => !empty($content['button_color']),
                        'name' => 'btn--' . (!empty($content['button_color']) ? $content['button_color'] : ''),
                    ],
                    [
                        'condition' => !empty($content['button_size']),
                        'name' => 'btn--' . (!empty($content['button_size']) ? $content['button_size'] : ''),
                    ],
                    [
                        'condition' => !empty($content['button_animation']),
                        'name' => 'btn--animation-' . (!empty($content['button_animation']) ? $content['button_animation'] : ''),
                    ],
                    [
                        'condition' => !empty($content['button_tracking']),
                        'name' => 'js-' . (!empty($content['button_tracking']) ? $content['button_tracking'] : '')
                    ]
                ]);
                ?>
                <?php if (player()->isLoggedIn()) : ?>
                    <?php
                    $spinButtonClass = getClass([
                        $buttonClass,
                        'spin-btn',
                        [
                            'condition' => empty($currentOffer),
                            'name' => 'btn--disabled',
                            'else-name' => 'js-spin-wheel-button',
                        ]
                    ]);
                    ?>
                    <button class="<?= $spinButtonClass; ?>"><?= $spinButtonText; ?></button>
                    <a class="offer-link <?= $buttonClass; ?>" href="<?= $content['button_url']; ?>"><?= $content['button_text']; ?></a>
                <?php else : ?>
                    <a href="#" onClick="login()" class="btn js-button-login"><?= $loginButtonText ?></a>
                <?php endif; ?>
                <?php
                $stickyButtonElement = ob_get_clean();
                get_template_part('campaigns/parts/cta-button-sticky', null, [
                    'button' => $stickyButtonElement,
                    'sticky' => empty($content['not_sticky_mobile_button']),
                ]);
                ?>

                <?php
                    if(isset($content['payment_logos_toggle'])) {
                        get_template_part('components/payment-logos', null, ['show' => $content['payment_logos_toggle']]);
                    }
                ?>

            </div>
        </div>
        <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_content']); ?>
    </div>

    <?php get_template_part('components/steps', null, ['cta_link' => !empty($content['button_url']) ? $content['button_url'] : '']); ?>
</div>