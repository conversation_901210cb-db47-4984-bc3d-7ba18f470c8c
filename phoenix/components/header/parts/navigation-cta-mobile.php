<?php
global $isStarPageHeaderLayout;
$customCTA = get_field('custom_header_cta', 'option');

if ($isStarPageHeaderLayout) {
    $pretext = '';
    $CTA_url = get_field('custom_header_cta_url');
    $CTA_text = get_field('custom_header_cta_text');
} else {
    $pretext = get_field('cta_pretext', 'option');
    $CTA_url = loginUrl();
    $CTA_text = get_field('cta_name', 'option');

    if ($customCTA) {
        // Backwards compatibility for custom header CTA
        $CTA_url  = get_field('custom_header_cta_url', 'option');
        $CTA_text = get_field('custom_header_cta_text', 'option');

        $customCTAButton = get_field('custom_header', 'option');
        if ($customCTAButton && !empty($customCTAButton['button_url']) && !empty($customCTAButton['button_text'])) {
            $CTA_url = $customCTAButton['button_url'];
            $CTA_text = $customCTAButton['button_text'];
        }
    }
}

if (player()->isLoggedIn() && !$customCTA): ?>
    <a href="<?= logoutUrl(); ?>" class="navigation__account-mobile">
        <?= get_field_tweaked('logout', 'option'); ?>
    </a>
<?php elseif ($isStarPageHeaderLayout) : ?>
    <?php if ($CTA_text) : ?>
        <a href="<?= $CTA_url; ?>" class="btn btn--primary btn--small navigation__cta-mobile<?= (!empty(get_field_tweaked('enable_quick_register_login', 'option')) ? " js-sidebar-panel-toggle" : ""); ?> js-button-register">
            <?= $CTA_text; ?>
        </a>
    <?php endif; ?>
<?php else : ?>
    <a href="<?= loginURL(); ?>" class="navigation__account-mobile<?= (!empty(get_field_tweaked('enable_quick_register_login', 'option')) ? " js-sidebar-panel-toggle" : ""); ?> js-button-login">
        <?= get_field_tweaked('login', 'option'); ?>
    </a>
<?php endif;
