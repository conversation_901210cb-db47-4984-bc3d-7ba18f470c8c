<?php
global $isStarPageHeaderLayout;
$customCTA = get_field('custom_header_cta', 'option');

if ($isStarPageHeaderLayout) {
    $pretext = '';
    $CTA_url = get_field('custom_header_cta_url');
    $CTA_text = get_field('custom_header_cta_text');
} else {
    $pretext = get_field('cta_pretext', 'option');
    $CTA_url = loginUrl();
    $CTA_text = get_field('cta_name', 'option');

    if ($customCTA) {
        // Backwards compatibility for custom header CTA
        $CTA_url  = get_field('custom_header_cta_url', 'option');
        $CTA_text = get_field('custom_header_cta_text', 'option');

        $customCTAButton = get_field('custom_header', 'option');
        if ($customCTAButton && !empty($customCTAButton['button_url']) && !empty($customCTAButton['button_text'])) {
            $CTA_url = $customCTAButton['button_url'];
            $CTA_text = $customCTAButton['button_text'];
        }

        $customCTA_loggedin = get_field('custom_header_cta_loggedin', 'option');
        if($customCTA_loggedin && player()->isLoggedIn()) {
            $CTA_url = get_field('custom_header_loggedin', 'option')['button_url'];
            $CTA_text = get_field('custom_header_loggedin', 'option')['button_text'];
        }
    }
}

if (player()->isLoggedIn() && !$customCTA): ?>
    <a href="<?= logoutUrl(); ?>" class="login-wrapper__logout">
        <?= get_field('logout', 'option'); ?>
    </a>
<?php else : ?>
    <?php if (!empty($pretext)) : ?>
        <p><?= $pretext; ?></p>
    <?php endif; ?>
    <?php if ($CTA_text) : ?>
        <a href="<?= $CTA_url; ?>" class="login-wrapper__cta btn btn--primary <?= (!empty(get_field('enable_quick_register_login')) ? " js-sidebar-panel-toggle" : ""); ?> js-button-register">
            <?= $CTA_text; ?>
        </a>
    <?php endif; ?>
<?php endif;
