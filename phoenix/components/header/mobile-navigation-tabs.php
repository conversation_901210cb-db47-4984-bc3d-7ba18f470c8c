<?php
if (function_exists('get_transient')) {
    $mobileMenu = get_transient('px_api_navigation_' . CURRENT_REGION);
}
?>
<div class="navigation navigation--mobile-tabs">
    <div class="navigation__menu navigation__menu--mobile-tabs">
        <ul class="menu">
            <?php
            // Use WordPress native menu directly (synced from API by BrandAPI)
            $syncedMenuName = 'Synced Menu - ' . CURRENT_REGION;
            $syncedMenu = wp_get_nav_menu_object($syncedMenuName);

            if ($syncedMenu && !get_field_tweaked('disable_mobile_nav_sync', 'option')) {
                $syncedMenuItems = wp_get_nav_menu_items($syncedMenu->term_id);

                if (!empty($syncedMenuItems)) {
                    foreach ($syncedMenuItems as $menuItem) {
                        if (!empty($menuItem->url) && !empty($menuItem->title)) {
                            // Get GTM tracking value for this menu item
                            $gtmAttribute = '';
                            $gtmValue = get_post_meta($menuItem->ID, '_menu_item_gtm_tracking', true);
                            if (!empty($gtmValue)) {
                                $gtmAttribute = ' data-gtm-tracking="' . esc_attr($gtmValue) . '"';
                            }

                            echo '<li><a href="' . esc_url($menuItem->url) . '"' . $gtmAttribute . '>' . esc_html($menuItem->title) . '</a></li>';
                        }
                    }
                }
            }
            // If transient doesn't exist or mobile nav sync is disabled, use WordPress menu
            else {
                if (empty($args['location'])) {
                    $args['location'] = 'mobile-menu_' . CURRENT_REGION;
                }

                if (has_nav_menu($args['location'])) {
                    wp_nav_menu([
                        'theme_location' => $args['location'],
                        'walker' => new Phnx_Walker_Nav_Menu(),
                        'sort_column' => 'menu_order',
                    ]);
                }
            }
            ?>
        </ul>
    </div>
</div>
