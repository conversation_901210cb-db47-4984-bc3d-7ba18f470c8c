<?php
    $args = empty($args) ? [] : $args;

    $steps = [];
    for ($ctr = 1; $ctr <= 3; $ctr++):
        $steps[$ctr] = get_field('step-' . $ctr);
    endfor;

    $args = array_merge([
        'include_stepper' => get_field('include_stepper'),
        'cta_link' => '',
        'steps' => $steps,
        'has_icons' => get_field('use_step_icons'),
        'background_color' => get_field('custom_background_color'),
        'text_color' => get_field('custom_text_color'),
        'has_link' => get_field('use_cta_link'),
        'title' => get_field('steps_title'),
        'vertical' => get_field('vertical_steps'),
    ], $args);

if ($args['include_stepper']) :
    $stepsClass = getClass([
        'stepper',
        [
            'condition' => !empty($args['has_icons']),
            'name' => 'stepper--icons',
        ],
        [
            'condition' => !empty($args['background_color']),
            'name' => 'stepper--background',
        ],
        [
            'condition' => !empty($args['text_color']),
            'name' => 'stepper--text-color',
        ],
        [
            'condition' => !empty($args['vertical']),
            'name' => 'stepper--vertical',
        ],
    ]);

    $customColors = '';
    $customColors .= !empty($args['background_color']) ? 'background-color:' . $args['background_color'] . ';' : '';
    $customColors .= !empty($args['text_color']) ? 'color:' . $args['text_color'] : '';
?>
	<div class="<?= $stepsClass; ?>" <?= !empty($customColors) ? 'style="' . $customColors . '"' : ""; ?>>
        <div class="container wrapper wrapper--md-80">
            <?php if (!empty($args['title'])): ?>
                <h4 class="stepper__title"><?= $args['title']; ?></h4>
            <?php endif; ?>
            <ul class="stepper__grid">
                <?php foreach ($args['steps'] as $stepCount => $step) : ?>
                    <li>
                        <?php ob_start(); ?>
                            <div class="stepper__count">
                                <?php if($args['has_icons']): ?>
                                    <img src="<?= $step['step_icon'] ?>" alt="Step <?= $stepCount; ?>" />
                                <?php else: ?>
                                    <h5><?= $stepCount; ?></h5>
                                <?php endif; ?>
                            </div>
                            <div class="stepper__content">
                                <?php if($step['header']): ?>
                                    <h4><?= $step['header']; ?></h4>
                                <?php endif; ?>
                                <p><?= $step['description']; ?></p>
                            </div>
                        <?php $stepElement = ob_get_clean(); ?>

                        <?php if(!empty($args['cta_link']) && $args['has_link']) : ?>
                            <a class="stepper__step" href="<?= $args['cta_link']; ?>">
                                <?= $stepElement; ?>
                            </a>
                        <?php else: ?>
                            <div class="stepper__step">
                                <?= $stepElement; ?>
                            </div>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
	</div>
<?php endif; ?>