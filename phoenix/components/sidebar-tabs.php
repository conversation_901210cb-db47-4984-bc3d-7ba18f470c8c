<?php
$sidebarTabs = [
    'class'       => '',
    'top'       => '',
    'tabs'    => [] // ['title' => '', 'content' => '', 'disabled' => false, 'active' => false]
];

$sidebarTabs = array_merge($sidebarTabs, $args);

$activeFlag = false;
foreach ($sidebarTabs['tabs'] as $key => $tab) {
    $sidebarTabs['tabs'][$key]['active'] = $tab['active'] ?? false;
    if($sidebarTabs['tabs'][$key]['active'])
        $activeFlag = false;
    $sidebarTabs['tabs'][$key]['slug'] = sanitize_title($tab['title']);
}

if (!$activeFlag)
    $sidebarTabs['tabs'][0]['active'] = true;

?>
<div class="sidebar-tabs <?= $sidebarTabs['class']; ?>">
    <div class="sidebar-tabs__sidebar">
        <?= $sidebarTabs['top']; ?>

        <div class="sidebar-tabs__title-list"  role="tablist">
        <?php foreach ($sidebarTabs['tabs'] as $key => $tab) : ?>
            <a
            role="tab"
            aria-selected="<?= !empty($tab['active']) ? 'true' : 'false'; ?>"
            aria-controls="sidebar-tab-panel-<?= $tab['slug']; ?>"
            id="sidebar-tab-<?= $tab['slug']; ?>"
            class="sidebar-tabs__title js-sidebar-tabs-trigger <?= !empty($tab['disabled']) ? 'sidebar-tabs__title--disabled' : ''; ?>">
                <?= $tab['title']; ?>
            </a>
        <?php endforeach; ?>
        </div>
    </div>

    <div class="sidebar-tabs__contents">
        <?php foreach ($sidebarTabs['tabs'] as $tab) : ?>
            <div
            role="tabpanel"
            id="sidebar-tab-panel-<?= $tab['slug']; ?>"
            data-content="sidebar-tab-panel-<?= $tab['slug']; ?>"
            class="sidebar-tabs__content js-sidebar-tabs-content <?= !empty($tab['active']) ? 'sidebar-tabs__content--visible' : ''; ?>">
                <?= $tab['content']; ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>