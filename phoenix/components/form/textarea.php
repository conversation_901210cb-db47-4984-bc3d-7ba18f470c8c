<?php
if (!is_array($args)) $args = []; // Fallback for array_merge below

$args = array_merge([
    'label' => '',
    'class' => '',
    'id' => '',
    'name' => '',
    'value' => '',
    'placeholder' => '',
    'rows' => '1',
    'disabled' => false,
    'required' => false,
    'dynamic-height' => false,
], $args);
?>
<label class="input <?= $args['class']; ?>">
    <textarea
        class="input__field <?= empty($args['dynamic-height']) ?: 'input__field--dynamic-height js-textarea-dynamic-height'; ?>"
        <?php if (!empty($args['id'])) : ?>id="<?= $args['id']; ?>" <?php endif; ?>
        <?php if (!empty($args['name'])) : ?>name="<?= $args['name']; ?>" <?php endif; ?>
        placeholder="<?= $args['placeholder']; ?>"
        rows="<?= $args['rows']; ?>"
        <?php if ($args['disabled']) : ?>disabled<?php endif; ?>
        <?php if ($args['required']) : ?>required<?php endif; ?>
        ><?= $args['value']; ?></textarea>
    <span class="input__label"><?= $args['label']; ?></span>
</label>