<?php
if (!is_array($args)) $args = []; // Fallback for array_merge below

$args = array_merge([
    'name' => '',
    'class' => '',
    'options' => [],
    'hints' => []
], $args);
?>
<div class="yes-no-radio <?= $args['class']; ?>">
    <?php foreach ($args['options'] as $option) : ?>
        <label class="yes-no-radio__option">
            <input
                class="yes-no-radio__field"
                <?php if (!empty($option['id'])) : ?>id="<?= $option['id']; ?>" <?php endif; ?>
                <?php if (!empty($args['name'])) : ?>name="<?= $args['name']; ?>" <?php endif; ?>
                value="<?= $option['value']; ?>"
                type="radio">
            <span class="yes-no-radio__label" data-hint-unselected="<?= ($args['hints']['unselected'] ?? ''); ?>" data-hint-selected="<?= ($args['hints']['selected'] ?? ''); ?>">
                <?= $option['label']; ?>
            </span>
            <div class="yes-no-radio__icon">
                <i class="yes-no-radio__unselected"><?= vector('radio-unselected'); ?></i>
                <i class="yes-no-radio__selected"><?= vector('checkmark-circle-filled'); ?></i>
            </div>
        </label>
    <?php endforeach; ?>
</div>