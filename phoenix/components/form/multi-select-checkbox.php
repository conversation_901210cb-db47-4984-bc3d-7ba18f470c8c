<?php
if (!is_array($args)) $args = []; // Fallback for array_merge below

$args = array_merge([
    'name' => '',
    'class' => '',
    'options' => [],
], $args);
?>
<div class="multi-select-checkbox <?= $args['class']; ?>">
    <?php foreach ($args['options'] as $option) : ?>
        <label class="multi-select-checkbox__option">
            <input
                class="multi-select-checkbox__field"
                <?php if (!empty($option['id'])) : ?>id="<?= $option['id']; ?>" <?php endif; ?>
                <?php if (!empty($args['name'])) : ?>name="<?= $args['name']; ?>[]" <?php endif; ?>
                value="<?= $option['value']; ?>"
                type="checkbox">
            <span class="multi-select-checkbox__label"><?= $option['label']; ?></span>
            <i class="multi-select-checkbox__icon"><?= vector('check'); ?></i>
        </label>
    <?php endforeach; ?>
</div>