/* Lint ------------------- */
/* global documentReady */
documentReady(function () {
    if (document.querySelector('.js-textarea-dynamic-height')) {
        const textareas = document.querySelectorAll('.js-textarea-dynamic-height');
        textareas.forEach(function (textarea) {
            textarea.addEventListener('input', () => {
                textarea.style.height = `${textarea.scrollHeight + 1}px`;
            });
        });


    }
});