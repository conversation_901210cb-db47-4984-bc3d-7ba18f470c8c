/* Lint ------------------- */
/* global documentReady */
documentReady(function () {
    if (document.querySelector('.js-feeback-slider-input')) {
        const feeback_slider_inputs = document.querySelectorAll('.js-feeback-slider-input');
        feeback_slider_inputs.forEach(function (feeback_slider_input) {
            feeback_slider_input.addEventListener('input', function (e) {
                feeback_slider_input.setAttribute('data-value', feeback_slider_input.value);
            });
        });
    }
});