.dropdown {
    $dropdown: &;

    display: inline-block;

    p:empty {
        display: none;
    }

    &__title {
        display: inline-flex;
        align-items: center;
        text-decoration: none;
        cursor: pointer;

        @include gap(2px);

        &:hover {
            text-decoration: underline;
        }
    }

    &__arrow {
        display: inline-block;
        line-height: 0;
        transition: all 0.5s;

        svg {
            width: 18px;
            height: 18px;

            path {
                fill: currentcolor;
            }
        }
    }

    &__text {
        display: none;
        transition: all 0.5s;
        animation: dropdown 0.5s;
    }

    &--show {
        #{$dropdown}__arrow{
            transform: rotateX(180deg);
        }

        #{$dropdown}__text{
            display: block;
        }
    }
}

@keyframes dropdown {

    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}