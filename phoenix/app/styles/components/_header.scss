header {
    .navigation {
        transition: all $transition;
        background: $header-background;
        border: none;
        box-sizing: border-box;
        height: $header-height;
        position: relative;
        width: 100%;
        z-index: 25;

        &--secondary {
            z-index: 24;
            background: $secondary-header-background;
            height: $secondary-header-height;
        }

        &--mobile-tabs {
            height: $mobile-navigation-tabs-height;
        }

        &__hide-on-scroll {
            transition: all $transition;
            opacity: 1;

            &--hidden {
                transform: translateY(-101%);
                opacity: 0;
            }
        }

        .has-transparent-header--scrolled &,
        .has-transparent-header:not(.page-scrolled) & {
            background: none;
            background-color: transparent;
            border: 0;
        }

        .login-wrapper {
            margin-left: auto;
            display: flex;
            justify-content: center;
            align-items: center;
            @include gap($gutter);

            p {
                color: $header-login-pretext;
                float: left;
                @include get-typography('label-bold');
                margin: 0;
            }

            &__cta {
                margin: 0 auto;
                display: flex;
                align-items: center;
                height: 44px;
                padding: 0 24px;
            }

            &__logout {
                color: $header-color;
                @include get-typography('label-bold');
                text-decoration: none;
            }
        }
    }
}
