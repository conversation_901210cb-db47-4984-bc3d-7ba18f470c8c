@mixin btn--normal {
    @include get-typography("heading-3");
    padding: 14px (2 * $gutter); // $gutter: 12px from variables.scss = 24px
}
@mixin btn--small {
    @include get-typography("label-bold");
    padding: 8px (1 * $gutter); // = 12px
}
@mixin btn--large {
    @include get-typography("heading-2");
    border-radius: $large-button-border-radius;

    padding: (1.5 * $gutter) (3 * $gutter); // $gutter: 12px from variables.scss = 36px
}

.btn {
    $btn: &;

    -webkit-appearance: none; // sass-lint:disable-line no-vendor-prefixes
    background: $button-background;
    border: 0;
    outline: none;
    border-radius: $button-border-radius;
    box-sizing: border-box;
    color: $button-color;
    cursor: pointer;
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    margin: 0;
    text-align: center;
    text-decoration: none;
    transition: all $transition;
    white-space: nowrap;

    @include btn--normal();

    &:hover {
        background: $button-hover;
    }

    &--primary {
        background: $button-background;
        color: $button-color;

        &:hover {
            background: $button-hover;
        }
    }

    &--secondary {
        background: $button-secondary-background;
        color: $button-secondary-color;

        &:hover {
            background: $button-secondary-hover;
        }
    }

    &--large {
        @include btn--large();
    }

    &--disabled,
    &--disabled:hover,
    &:disabled {
        background: $button-disabled-background;
        color: $button-disabled-color;
        cursor: default;
        pointer-events: none;
    }

    &--white {
        background: $color-primary-white;
        color: $color-font-dark;

        &:hover {
            background: $color-primary-white;
            color: $color-font-dark;
            opacity: 0.9;
        }
    }

    &--small {
        @include btn--small();

        @include create-classes-by-breakpoints() {
            @include btn--small();
        }
    }

    &--full {
        width: 100%;
        display: block;

        @include create-classes-by-breakpoints() {
            width: 100%;
            display: block;
        }
    }
    &--rounded {
        border-radius: $button-border-radius-rounded;

        @include create-classes-by-breakpoints() {
            border-radius: $button-border-radius-rounded;
        }
    }

    &--outlined {
        background: transparent;
        border: 1px solid $button-background;
        padding-top: 13px;
        padding-bottom: 13px;
        color: $button-background;

        &:hover {
            background: $button-background;
            color: $button-color;
        }

        &#{$btn}--small {
            padding-top: 7px;
            padding-bottom: 7px;

            @include create-classes-by-breakpoints() {
                padding-top: 7px;
                padding-bottom: 7px;
            }
        }

        &#{$btn}--large {
            padding-top: (1.5 * $gutter) - 1px;
            padding-bottom: (1.5 * $gutter) - 1px;

            @include create-classes-by-breakpoints() {
                padding-top: (1.5 * $gutter) - 1px;
                padding-bottom: (1.5 * $gutter) - 1px;
            }
        }

        &#{$btn}--primary {
            background: transparent;
            border: 1px solid $button-background;
            color: $button-background;

            &:hover {
                background: $button-background;
                color: $button-color;
            }
        }

        &#{$btn}--secondary {
            background: transparent;
            border: 1px solid $button-secondary-background;
            color: $button-secondary-background;

            &:hover {
                background: $button-secondary-background;
                color: $button-secondary-color;
            }
        }

        &#{$btn}--white {
            background-color: transparent;
            border: 1px solid $color-font-light;
            color: $color-font-light;

            &:hover {
                background-color: $color-font-light;
                color: $color-font-dark;
            }
        }

        &#{$btn}--disabled,
        &#{$btn}--disabled:hover,
        &:disabled {
            background: transparent;
            border: 1px solid $button-disabled-background;
            color: $button-disabled-color;
            cursor: default;
        }
    }

    &--transparent {
        background: transparent;
    }

    &--no-hover {
        &:hover {
            background: initial;
            color: currentColor;
        }
    }

    &--logo {
        line-height: 0 !important;
        padding-left: 0;
        padding-right: 0;

        span {
            @include get-typography("heading-3");
            font-family: $font-family-primary;
            margin-left: 12px;
        }

        svg {
            vertical-align: middle;
        }

        &-white {
            img {
                filter: brightness(0) invert(1);
            }

            &:not(.swish) {
                svg,
                path {
                    fill: $button-color;
                }
            }

            &.swish path:not([fill-rule="evenodd"]) {
                fill: $button-color;
            }
        }
    }

    &__icon {
        display: block;
        width: 24px;
        height: 24px;
        max-height: 100%;

        svg {
            width: 100%;
            height: 100%;

            path {
                fill: currentColor;
            }
        }
    }

    &--small {
        & > #{$btn}__icon {
            width: 20px;
            height: 20px;
        }

        @include create-classes-by-breakpoints() {
            & > #{$btn}__icon {
                margin-top: -2px;
                margin-bottom: -2px;
            }
        }
    }

    &--with-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        @include gap($gutter);
    }

    &__wrapper {
        margin-top: 28px;

        @include to(md) {
            text-align: center;
        }

        &--sticky {
            display: none;
            background-color: $sticky-button-background;
            padding: $gutter;
            margin-top: 0;

            #{$btn} {
                display: block;
                width: 100%;
            }

            #{$btn}__split {
                box-sizing: border-box;
                width: 100%;

                #{$btn} {
                    @include get-typography("heading-4-bold");
                }
            }

            #{$btn}--secondary {
                background-color: $button-background;
            }

            .overlay & {
                display: none;
            }
        }

        @include to(sm) {
            &--desktop {
                display: none;
            }

            &--sticky {
                display: block;

                &:not([data-content=""]) {
                    // for sliders to show only the button for the active slider
                    display: none;

                    &.visible {
                        display: block;
                    }
                }

                #{$btn}--secondary {
                    background-color: $button-background;
                    color: $button-color;
                }
            }
        }
    }

    &__container {
        margin-top: 28px;
    }

    &__split {
        display: inline-flex;
        @include gap(1px);
        align-items: center;
        margin: auto;

        @include from(sm) {
            @include gap(12px);
        }

        @include to(sm) {
            #{$btn} {
                border-radius: 0;
                padding-left: 1 * $gutter;
                padding-right: 1 * $gutter;

                @include from(xs) {
                    padding-left: 2 * $gutter;
                    padding-right: 2 * $gutter;
                }

                &:first-child {
                    border-top-left-radius: $button-border-radius;
                    border-bottom-left-radius: $button-border-radius;
                }

                &:last-child {
                    border-top-right-radius: $button-border-radius;
                    border-bottom-right-radius: $button-border-radius;
                }
            }
        }
    }

    $btn-animation-duration: 2.5s;
    &--animation {
        &-shine {
            position: relative;
            overflow: hidden;

            &::after {
                content: "";
                animation: shiny (2 * $btn-animation-duration) infinite;
                background: rgba(255, 255, 255, 0.3);
                height: 100%;
                left: -64%;
                position: absolute;
                top: 0;
                width: 60%;
            }
        }

        &-shadow-pulse {
            animation: btn-shadow-pulse-primary $btn-animation-duration infinite;

            &#{$btn}--primary {
                animation: btn-shadow-pulse-primary $btn-animation-duration infinite;
            }

            &#{$btn}--secondary {
                animation: btn-shadow-pulse-secondary $btn-animation-duration infinite;
            }
        }
    }
}
