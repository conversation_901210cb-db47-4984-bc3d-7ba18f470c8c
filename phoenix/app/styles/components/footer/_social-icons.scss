.social-icons {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    justify-content: end;
    margin: 0;
    padding: 0;
    list-style-type: none;
    justify-self: self-end;

    @include gap(6px);

    @include from(xxl) {
        @include gap(12px);
    }

    a {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 52px;
        height: 52px;
        background-color: $footer-social-icon-background;
        border-radius: $border-radius-sm;
        line-height: 60px;
        text-align: center;

        svg,
        path,
        g {
            fill: $footer-social-icon-color;
        }

        svg {
            opacity: 0.75;
        }

        &:hover {

            svg {
                opacity: 1;
            }
        }
    }
}
