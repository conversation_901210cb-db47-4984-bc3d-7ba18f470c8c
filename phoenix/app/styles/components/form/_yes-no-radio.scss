.yes-no-radio {
    $self: &;
    display: flex;
    @include gap(12px);

    &__option {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        padding: 12px;
        cursor: pointer;
        user-select: none;
        transition: all $transition;
        background-color: $yes-no-radio-background;
        color: $yes-no-radio-color;
        border-radius: $border-radius-lg;
        border: 1px solid rgba($yes-no-radio-border, 0.1);

        &:has(#{$self}__field:checked) {
            background-color: $yes-no-radio-background-active;
            border-color: $yes-no-radio-color-active;

            #{$self}__unselected {
                height: 0;
                width: 0;
                opacity: 0;
            }

            #{$self}__selected {
                opacity: 1;
                height: 24px;
                width: 24px;
            }

            #{$self}__label {
                &::after {
                    content: attr(data-hint-selected);
                }

                &[data-hint-selected=""]::after {
                    content: none;
                }
            }
        }
    }

    &__field {
        position: absolute;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    &__icon {
        display: flex;
        align-items: center;
    }

    &__unselected,
    &__selected {
        display: block;
        height: 0;
        width: 0;
        opacity: 0;
        transition: opacity $transition;

        svg {
            height: 100%;
            width: 100%;
        }
    }

    &__unselected {
        opacity: 1;
        height: 24px;
        width: 24px;

        path {
            fill: $color-font-supportive;
        }
    }

    &__selected {
        path {
            fill: $yes-no-radio-color-active-icon;
        }
    }

    &__label {
        display: block;
        @include get-typography("label-bold");
        margin: 0;
        text-align: left;

        &::after {
            content: attr(data-hint-unselected);
            @include get-typography("caption");
            display: block;
            margin-top: 2px;
            color: $color-font-supportive;
        }

        &[data-hint-unselected=""]::after {
            content: none;
        }
    }
}
