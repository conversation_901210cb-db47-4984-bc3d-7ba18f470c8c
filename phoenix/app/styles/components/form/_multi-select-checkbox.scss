.multi-select-checkbox {
    $self: &;
    display: flex;
    @include gap(4px);
    flex-wrap: wrap;

    &--center {
        align-items: center;
        justify-content: center;
    }

    &__option {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        border-radius: 18px;
        cursor: pointer;
        user-select: none;
        transition: all $transition;
        color: $multi-select-checkbox-color;
        background-color: $multi-select-checkbox-background;

        &:has(#{$self}__field:checked) {
            color: $multi-select-checkbox-color-active;
            background-color: $multi-select-checkbox-background-active;

            #{$self}__icon {
                opacity: 1;
                width: 24px;
                height: 24px;
            }
        }
    }

    &__field {
        position: absolute;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    &__label {
        display: block;
        @include get-typography("label-bold");
        margin: 0;
    }

    &__icon {
        margin: -2px -4px -2px 6px;
        width: 0;
        height: 0;
        opacity: 0;
        transition: all $transition;

        svg {
            width: 100%;
            height: 100%;

            path {
                fill: currentColor;
            }
        }
    }
}
