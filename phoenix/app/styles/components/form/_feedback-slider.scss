.feedback-slider {
    $self: &;

    $thumb-size: 43px;
    $range-bar-size: 4px;

    &__range {
        position: relative;
        width: 100%;
        height: $thumb-size;
    }

    &__input {
        -webkit-appearance: none;
        width: 100%;
        height: $thumb-size;
        background: transparent;
        outline: none;
        z-index: 4;
        position: absolute;
        margin: 0;
        padding: 0;
        top: 0;
        left: 0;
        right: 0;

        &::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;

            width: $thumb-size;
            height: $thumb-size;
            border-radius: 50%;
            background-color: $feedback-slider-thumb;
            background-position: center;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="43" height="43" fill="none" viewBox="0 0 43 43"><path fill="#{encodecolor($feedback-slider-thumb-icon)}" d="M32.57 25.125a.694.694 0 0 1-.195-.507c0-.205.065-.374.195-.506l2.762-2.823-2.762-2.823a.694.694 0 0 1-.195-.506c0-.206.065-.374.195-.507a.664.664 0 0 1 .496-.2c.2 0 .366.067.495.2l3.259 3.33c.07.072.12.15.15.234.03.085.045.175.045.272a.825.825 0 0 1-.044.271.642.642 0 0 1-.151.236l-3.258 3.329a.664.664 0 0 1-.496.199.664.664 0 0 1-.496-.2zM9.438 24.957l-3.259-3.258a.617.617 0 0 1-.15-.23.778.778 0 0 1-.045-.266c0-.095.015-.183.045-.266.03-.082.08-.16.15-.23l3.258-3.258a.672.672 0 0 1 .496-.195c.201 0 .366.065.496.195s.195.295.195.495c0 .201-.065.367-.195.496l-2.762 2.763 2.762 2.762c.13.13.195.296.195.496s-.065.366-.195.496a.672.672 0 0 1-.496.195.672.672 0 0 1-.495-.195z"/></svg>');
            cursor: pointer;
            position: relative;
        }

        &::-moz-range-thumb {
            width: $thumb-size;
            height: $thumb-size;
            border-radius: 50%;
            background-color: $feedback-slider-thumb;
            background-position: center;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="43" height="43" fill="none" viewBox="0 0 43 43"><path fill="#{encodecolor($feedback-slider-thumb-icon)}" d="M32.57 25.125a.694.694 0 0 1-.195-.507c0-.205.065-.374.195-.506l2.762-2.823-2.762-2.823a.694.694 0 0 1-.195-.506c0-.206.065-.374.195-.507a.664.664 0 0 1 .496-.2c.2 0 .366.067.495.2l3.259 3.33c.07.072.12.15.15.234.03.085.045.175.045.272a.825.825 0 0 1-.044.271.642.642 0 0 1-.151.236l-3.258 3.329a.664.664 0 0 1-.496.199.664.664 0 0 1-.496-.2zM9.438 24.957l-3.259-3.258a.617.617 0 0 1-.15-.23.778.778 0 0 1-.045-.266c0-.095.015-.183.045-.266.03-.082.08-.16.15-.23l3.258-3.258a.672.672 0 0 1 .496-.195c.201 0 .366.065.496.195s.195.295.195.495c0 .201-.065.367-.195.496l-2.762 2.763 2.762 2.762c.13.13.195.296.195.496s-.065.366-.195.496a.672.672 0 0 1-.496.195.672.672 0 0 1-.495-.195z"/></svg>');
            cursor: pointer;
            position: relative;
        }
    }

    &__track {
        width: 100%;
        height: $range-bar-size;
        background-color: $feedback-slider-bar;
        z-index: 1;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0;
        right: 0;
    }

    &__bar {
        background-color: $feedback-slider-thumb;
        height: $range-bar-size;
        width: 0;
        max-width: 50%;
        border-radius: $border-radius-md;
        z-index: 2;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }

    @for $i from 1 through 2 {
        &__input[data-value="#{$i}"] ~ &__bar {
            right: 50%;
            width: #{25% * (3 - $i)};
        }

        &__input[data-value="#{$i}"] ~ &__mark {
            @for $j from $i through 2 {
                &--#{$j} {
                    background-color: $feedback-slider-thumb;
                }
            }
        }
    }
    @for $i from 4 through 5 {
        &__input[data-value="#{$i}"] ~ &__bar {
            left: 50%;
            width: #{25% * ($i - 3)};
        }

        &__input[data-value="#{$i}"] ~ &__mark {
            @for $j from 4 through $i {
                &--#{$j} {
                    background-color: $feedback-slider-thumb;
                }
            }
        }
    }

    &__mark {
        width: 18px;
        height: 18px;
        position: absolute;
        background-color: $feedback-slider-bar;
        border-radius: 50%;
        top: 50%;
        left: 0;
        z-index: 3;

        &--1 {
            left: 0;
            transform: translate(0, -50%);
        }

        &--2 {
            left: 25%;
            transform: translate(0, -50%);
        }

        &--3 {
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: $feedback-slider-thumb;
        }

        &--4 {
            left: 75%;
            transform: translate(-100%, -50%);
        }

        &--5 {
            left: 100%;
            transform: translate(-100%, -50%);
        }
    }

    &__labels {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        @include get-typography('footnote');
        color: $feedback-slider-bar;
    }
}
