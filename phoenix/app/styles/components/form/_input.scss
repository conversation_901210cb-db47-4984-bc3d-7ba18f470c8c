.input {
    display: block;
    position: relative;

    &__field {
        box-sizing: border-box;
        display: block;
        background-color: $input-background;
        border: 0;
        border-bottom: 1px solid $input-border;
        padding: 24px 12px 6px;
        width: 100%;
        max-width: 100%;
        border-radius: 3px 3px 0 0;

        text-decoration: none;
        outline: none;
        resize: both;

        color: $input-color;
        @include get-typography('heading-5');

        transition: all $transition;

        &::placeholder {
            color: transparent;
            text-overflow: ellipsis;
        }

        &:focus::placeholder {
            color: $input-placeholder;
        }

        &:focus,
        &:hover {
            border-bottom: 1px solid $input-border-active;
        }

        &:disabled {
            color: $input-label-disabled;
            border-bottom: 1px solid $input-border-disabled;
        }

        &--dynamic-height {
            overflow-y: hidden;
            transition: all $transition, height 1ms;
        }
    }

    &__label {
        display: block;
        pointer-events: none;
        margin: 0;
        max-width: 100%;
        position: absolute;
        top: 17px;
        left: 12px;
        right: 12px;
        color: $input-label;
        @include get-typography('heading-5');
        white-space: nowrap;
        overflow: hidden;
        text-align: left;
        text-overflow: ellipsis;

        transition: all $transition;
    }

    &__field:disabled ~ &__label{
        color: $input-label-disabled;
    }

    &__field:not(:placeholder-shown) ~ &__label,
    &__field:focus ~ &__label {
        $input-label-fill: $input-label !default;
        color: $input-label-fill;

        @include get-typography('caption');
        top: 7px;
    }
}