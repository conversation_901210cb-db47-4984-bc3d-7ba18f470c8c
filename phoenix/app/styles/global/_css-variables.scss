// Header
body {
    --compliance-height: 0px;

    --header-height: 44px;
    --mobile-navigation-tabs-height: 0px;
    --secondary-header-height: 47px;

    @include from(md) {
        --header-height: 64px;
    }

    --tabs-height: 0px;
    --jump-links-height: 0px;
    --casino-info-bar-height: 0px;

    &:has(.casino-info-bar) {
        --casino-info-bar-height: 50px;
    }
}

body:has(.navigation__menu--mobile-tabs) {
    --mobile-navigation-tabs-height: 40px;

    @include from(lg) {
        --mobile-navigation-tabs-height: 0px;
    }
}

.has-no-header {
    --header-height: 0px;
}

.has-tabs {
    --tabs-height: 43px;
    @include from(md) {
        --tabs-height: 48px;
    }
}

.has-compliance {
    --compliance-height: 44px;

    &--gga {
        @include from(md) {
            --compliance-height: 84px;
        }
    }
}

.has-jump-links  {
    --jump-links-height: 44px;
    @include from(md) {
        --jump-links-height: 60px;
    }
}