@mixin fixed-top($top-position: 0px) {
    position: fixed;
    top: calc($top-position + $compliance-height);
}

@mixin fixed-top-full-screen($top-position: 0px) {
    position: fixed;
    top: calc($top-position + $compliance-height);
    height: calc(100% - ($top-position + $compliance-height));
}

@mixin full-screen($top-position: 0px) {
    height: calc(100vh - ($top-position + $header-height + $mobile-navigation-tabs-height + $compliance-height + $tabs-height + $jump-links-height));
}

@mixin min-full-screen($top-position: 0px) {
    min-height: calc(100vh - ($top-position + $header-height + $mobile-navigation-tabs-height + $compliance-height + $tabs-height + $jump-links-height)) !important;
}