.sp-block-faq {
    display: flex;
    flex-direction: column;
    @include gap(20px);

    @include from(md) {
        flex-direction: row;
        @include gap(100px);
    }

    &__column {
        width: 100%;

        @include from(md) {
            flex-basis: 50%;
            width: 50%;
        }

        &--image {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    &__questions {
        margin: auto;
        margin-top: 28px;
    }

    &__item {
        display: flex;

        &:not(:last-child) {
            margin-bottom: 12px;
        }

        &--outlined {
            display: block;
            width: 100%;
            padding: $gutter;
            border: 1px solid currentcolor;
            border-radius: $border-radius-md;

            .dropdown__title{
                display: flex;
                justify-content: space-between;
            }

            .dropdown__text{
                margin-top: $gutter;
            }
        }

        &--box {
            padding: 12px;
            border-radius: $border-radius-lg;
        }
    }

    &__image {
        max-width: 230px;

        @include from(md) {
            max-width: 460px
        }
    }

    &__question {
        margin: 0;
        margin-bottom: 6px;
        @include get-typography('heading-4');
    }

    &__answer {
        @include get-typography('label');
    }

    &__more-info {
        margin-top: 28px;
        text-align: center;
    }

    &__help-btn {
        display: inline-flex;
        align-items: center;
        margin-top: 64px;
        float: right;
        @include gap(8px);

        svg, path {
            fill: currentcolor;
        }
    }

    .dropdown__arrow path {
        fill: $sp-block-color-link;
    }
}
